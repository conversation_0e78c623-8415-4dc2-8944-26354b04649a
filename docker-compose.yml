services:
  postgres:
    image: postgres:15
    container_name: ai_accounting_postgres
    environment:
      POSTGRES_DB: ai_accounting
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  accounting-service:
    build: 
      context: ./accounting-service
      dockerfile: Dockerfile
    container_name: ai_accounting_service
    environment:
      DATABASE_URL: ********************************************/ai_accounting
      JWT_SECRET_KEY: your-super-secret-jwt-key-change-in-production
      JWT_ALGORITHM: HS256
      JWT_ACCESS_TOKEN_EXPIRE_MINUTES: 30
      JWT_SESSION_TOKEN_EXPIRE_MINUTES: 480
    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
    volumes:
      - ./accounting-service:/app
    command: uvicorn main:app --host 0.0.0.0 --port 8000 --reload

  agent-service:
    build:
      context: ./agent-service
      dockerfile: Dockerfile
    container_name: ai_agent_service
    environment:
      DATABASE_URL: ********************************************/ai_accounting
      ACCOUNTING_SERVICE_URL: http://accounting-service:8000
      OPENAI_API_KEY: ${OPENAI_API_KEY:-}
      AZURE_OPENAI_ENDPOINT: ${AZURE_OPENAI_ENDPOINT:-}
      AZURE_OPENAI_API_KEY: ${AZURE_OPENAI_API_KEY:-}
    ports:
      - "8001:8001"
    depends_on:
      - accounting-service
    volumes:
      - ./agent-service:/app
    command: uvicorn main:app --host 0.0.0.0 --port 8001 --reload

volumes:
  postgres_data:
