from sqlalchemy import Column, Inte<PERSON>, String, Boolean, DateTime, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from database import Base


class Customer(Base):
    """
    Customer model - Customers for each tenant
    """
    __tablename__ = "customers"

    id = Column(Integer, primary_key=True, index=True)
    tenant_id = Column(Integer, ForeignKey("tenants.id"), nullable=False, index=True)
    customer_number = Column(String(20), nullable=False, index=True)
    name = Column(String(255), nullable=False, index=True)
    organization_number = Column(String(20))
    contact_person = Column(String(100))
    email = Column(String(255))
    phone = Column(String(20))
    address = Column(String(255))
    postal_code = Column(String(10))
    city = Column(String(100))
    country = Column(String(100))
    payment_terms = Column(String(50))
    credit_limit = Column(Integer, default=0)
    is_active = Column(Boolean, default=True, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    tenant = relationship("Tenant", back_populates="customers")
    receivables = relationship("Receivable", back_populates="customer")

    def __repr__(self):
        return f"<Customer(id={self.id}, tenant_id={self.tenant_id}, name='{self.name}')>"
