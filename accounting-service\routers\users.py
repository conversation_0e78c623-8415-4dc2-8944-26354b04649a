from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from database import get_db
from models import User, UserTenant, Tenant, Role
from schemas.user import UserResponse, UserTenantResponse, UserUpdate
from core.dependencies import get_current_user

router = APIRouter()


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(
    current_user: User = Depends(get_current_user)
):
    """Get current user information"""
    return current_user


@router.put("/me", response_model=UserResponse)
async def update_current_user(
    user_update: UserUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Update current user information"""
    # Update user fields
    for field, value in user_update.model_dump(exclude_unset=True).items():
        setattr(current_user, field, value)
    
    await db.commit()
    await db.refresh(current_user)
    
    return current_user


@router.get("/me/tenants", response_model=List[UserTenantResponse])
async def get_user_tenants(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get all tenants the current user has access to"""
    result = await db.execute(
        select(UserTenant, Tenant, Role)
        .join(Tenant, UserTenant.tenant_id == Tenant.id)
        .join(Role, UserTenant.role_id == Role.id)
        .where(
            UserTenant.user_id == current_user.id,
            UserTenant.is_active == True,
            Tenant.is_active == True
        )
        .order_by(Tenant.company_name)
    )
    
    user_tenants = []
    for user_tenant, tenant, role in result.fetchall():
        user_tenants.append(UserTenantResponse(
            tenant_id=tenant.id,
            tenant_name=tenant.company_name,
            role_id=role.id,
            role_name=role.name,
            is_active=user_tenant.is_active
        ))
    
    return user_tenants
