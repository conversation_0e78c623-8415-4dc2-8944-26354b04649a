from sqlalchemy import <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, String, DateTime, ForeignKey, Boolean, JSON
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from database import Base


class CompanySettings(Base):
    """
    CompanySettings model - Company-specific settings for each tenant
    """
    __tablename__ = "company_settings"

    id = Column(Integer, primary_key=True, index=True)
    tenant_id = Column(Integer, ForeignKey("tenants.id"), nullable=False, unique=True, index=True)
    
    # Company information
    organization_number = Column(String(20))
    vat_number = Column(String(20))
    address = Column(String(255))
    postal_code = Column(String(10))
    city = Column(String(100))
    country = Column(String(100), default="Sweden")
    phone = Column(String(20))
    email = Column(String(255))
    website = Column(String(255))
    
    # Accounting settings
    default_currency = Column(String(3), default="SEK")
    fiscal_year_start_month = Column(Integer, default=1)  # 1 = January
    chart_of_accounts_template = Column(String(50), default="BAS2024")
    
    # AI system settings
    ai_system_api_key = Column(String(255))  # For system authentication
    ai_automation_enabled = Column(Boolean, default=True)
    
    # Other settings stored as JSON
    additional_settings = Column(JSON)
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    tenant = relationship("Tenant", back_populates="company_settings")

    def __repr__(self):
        return f"<CompanySettings(id={self.id}, tenant_id={self.tenant_id})>"
