from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Numeric, Date, Text
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from database import Base


class Voucher(Base):
    """
    Voucher model - Accounting vouchers for each tenant
    """
    __tablename__ = "vouchers"

    id = Column(Integer, primary_key=True, index=True)
    tenant_id = Column(Integer, ForeignKey("tenants.id"), nullable=False, index=True)
    voucher_number = Column(Integer, nullable=False, index=True)
    voucher_date = Column(Date, nullable=False, index=True)
    description = Column(String(500), nullable=False)
    reference = Column(String(100))
    created_by = Column(Integer, ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    tenant = relationship("Tenant", back_populates="vouchers")
    creator = relationship("User")
    rows = relationship("VoucherRow", back_populates="voucher", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Voucher(id={self.id}, tenant_id={self.tenant_id}, voucher_number={self.voucher_number})>"


class VoucherRow(Base):
    """
    VoucherRow model - Individual rows in accounting vouchers
    """
    __tablename__ = "voucher_rows"

    id = Column(Integer, primary_key=True, index=True)
    voucher_id = Column(Integer, ForeignKey("vouchers.id"), nullable=False, index=True)
    account_id = Column(Integer, ForeignKey("accounts.id"), nullable=False, index=True)
    debit_amount = Column(Numeric(15, 2), default=0, nullable=False)
    credit_amount = Column(Numeric(15, 2), default=0, nullable=False)
    description = Column(String(500))
    dimension_1 = Column(String(50))  # Cost center, project, etc.
    dimension_2 = Column(String(50))  # Department, etc.
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    voucher = relationship("Voucher", back_populates="rows")
    account = relationship("Account", back_populates="voucher_rows")

    def __repr__(self):
        return f"<VoucherRow(id={self.id}, voucher_id={self.voucher_id}, account_id={self.account_id})>"
