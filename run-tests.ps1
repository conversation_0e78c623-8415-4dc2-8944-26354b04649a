# Simple test runner with clean output

Write-Host "🧪 Running AI Accounting System Tests" -ForegroundColor Green
Write-Host "=====================================" -ForegroundColor Green

# Test Accounting Service
Write-Host "`n📊 Accounting Service Tests:" -ForegroundColor Cyan
$accountingOutput = docker-compose exec -T accounting-service python -m pytest tests/ --tb=no -q 2>&1
$accountingExitCode = $LASTEXITCODE

if ($accountingExitCode -eq 0) {
    Write-Host "✅ PASSED" -ForegroundColor Green
    # Extract just the summary line
    $summary = $accountingOutput | Where-Object { $_ -match "passed" } | Select-Object -Last 1
    if ($summary) {
        Write-Host "   $summary" -ForegroundColor Gray
    }
} else {
    Write-Host "❌ FAILED" -ForegroundColor Red
    Write-Host "   Some tests failed. Run with -v for details:" -ForegroundColor Yellow
    Write-Host "   docker-compose exec accounting-service python -m pytest tests/ -v" -ForegroundColor Yellow
}

# Test Agent Service
Write-Host "`n🤖 Agent Service Tests:" -ForegroundColor Cyan
$agentOutput = docker-compose exec -T agent-service python -m pytest tests/ --tb=no -q 2>&1
$agentExitCode = $LASTEXITCODE

if ($agentExitCode -eq 0) {
    Write-Host "✅ PASSED" -ForegroundColor Green
    # Extract just the summary line
    $summary = $agentOutput | Where-Object { $_ -match "passed" } | Select-Object -Last 1
    if ($summary) {
        Write-Host "   $summary" -ForegroundColor Gray
    }
} else {
    Write-Host "❌ FAILED" -ForegroundColor Red
    Write-Host "   Some tests failed. Run with -v for details:" -ForegroundColor Yellow
    Write-Host "   docker-compose exec agent-service python -m pytest tests/ -v" -ForegroundColor Yellow
}

# Overall summary
Write-Host "`n📋 Test Summary:" -ForegroundColor Green
if ($accountingExitCode -eq 0 -and $agentExitCode -eq 0) {
    Write-Host "🎉 All tests passed!" -ForegroundColor Green
} else {
    Write-Host "⚠️  Some tests failed. Check details above." -ForegroundColor Yellow
}

Write-Host "`n💡 Tip: For detailed output, run individual test commands shown above." -ForegroundColor Cyan
