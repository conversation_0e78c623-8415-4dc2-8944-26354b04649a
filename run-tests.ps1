# Simple test runner with clean output

Write-Host "Running AI Accounting System Tests" -ForegroundColor Green
Write-Host "===================================" -ForegroundColor Green

# Test Accounting Service
Write-Host "`nTesting Accounting Service..." -ForegroundColor Cyan
docker-compose exec -T accounting-service python -m pytest tests/ --tb=no -q
$accountingResult = $LASTEXITCODE

if ($accountingResult -eq 0) {
    Write-Host "PASSED - Accounting Service" -ForegroundColor Green
} else {
    Write-Host "FAILED - Accounting Service" -ForegroundColor Red
    Write-Host "Run for details: docker-compose exec accounting-service python -m pytest tests/ --verbose" -ForegroundColor Yellow
}

# Test Agent Service
Write-Host "`nTesting Agent Service..." -ForegroundColor Cyan
docker-compose exec -T agent-service python -m pytest tests/ --tb=no -q
$agentResult = $LASTEXITCODE

if ($agentResult -eq 0) {
    Write-Host "PASSED - Agent Service" -ForegroundColor Green
} else {
    Write-Host "FAILED - Agent Service" -ForegroundColor Red
    Write-Host "Run for details: docker-compose exec agent-service python -m pytest tests/ --verbose" -ForegroundColor Yellow
}

# Overall summary
Write-Host "`nTest Summary:" -ForegroundColor Green
if ($accountingResult -eq 0 -and $agentResult -eq 0) {
    Write-Host "All tests passed!" -ForegroundColor Green
} else {
    Write-Host "Some tests failed. Check details above." -ForegroundColor Yellow
}
