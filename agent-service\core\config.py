from pydantic_settings import BaseSettings
from typing import Optional


class Settings(BaseSettings):
    """Agent service settings"""
    
    # Service URLs
    accounting_service_url: str = "http://localhost:8000"
    
    # AI Configuration
    openai_api_key: Optional[str] = None
    azure_openai_endpoint: Optional[str] = None
    azure_openai_api_key: Optional[str] = None
    azure_openai_api_version: str = "2024-02-15-preview"
    
    # Default AI provider
    default_ai_provider: str = "openai"  # "openai" or "azure"
    
    # AI Model settings
    default_model: str = "gpt-4"
    max_tokens: int = 2000
    temperature: float = 0.1
    
    # API Configuration
    api_v1_str: str = "/api/v1"
    project_name: str = "AI Agent Service"
    
    # Environment
    environment: str = "development"
    debug: bool = True
    
    class Config:
        env_file = ".env"
        case_sensitive = False


settings = Settings()
