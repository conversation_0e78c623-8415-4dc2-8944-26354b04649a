from fastapi import Fast<PERSON><PERSON>, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
import logging

from routers import agent
from core.config import settings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="AI Agent Service",
    description="AI-orchestrated agent for accounting system interactions",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(agent.router, prefix="/agent", tags=["Agent"])


@app.get("/")
async def root():
    """Health check endpoint"""
    return {
        "message": "AI Agent Service is running",
        "version": "1.0.0",
        "status": "healthy"
    }


@app.get("/health")
async def health_check():
    """Detailed health check"""
    try:
        # Test AI provider availability
        from ai import AIProviderFactory
        
        # Try to create a provider (this will validate configuration)
        try:
            provider = AIProviderFactory.create_provider()
            ai_status = "configured"
        except Exception as e:
            ai_status = f"error: {str(e)}"
        
        return {
            "status": "healthy",
            "ai_provider": ai_status,
            "accounting_service_url": settings.accounting_service_url,
            "version": "1.0.0"
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=503, detail="Service unhealthy")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)
