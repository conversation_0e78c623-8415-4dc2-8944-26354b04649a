from typing import Optional
from .providers import AIProvider, OpenAIProvider, AzureOpenAIProvider
from core.config import settings


class AIProviderFactory:
    """Factory for creating AI providers"""
    
    @staticmethod
    def create_provider(
        provider_type: Optional[str] = None,
        **kwargs
    ) -> AIProvider:
        """Create an AI provider instance"""
        
        provider_type = provider_type or settings.default_ai_provider
        
        if provider_type == "openai":
            api_key = kwargs.get("api_key") or settings.openai_api_key
            if not api_key:
                raise ValueError("OpenAI API key is required")
            return OpenAIProvider(api_key=api_key)
        
        elif provider_type == "azure":
            endpoint = kwargs.get("endpoint") or settings.azure_openai_endpoint
            api_key = kwargs.get("api_key") or settings.azure_openai_api_key
            api_version = kwargs.get("api_version") or settings.azure_openai_api_version
            
            if not endpoint or not api_key:
                raise ValueError("Azure OpenAI endpoint and API key are required")
            
            return AzureOpenAIProvider(
                endpoint=endpoint,
                api_key=api_key,
                api_version=api_version
            )
        
        else:
            raise ValueError(f"Unsupported AI provider: {provider_type}")
    
    @staticmethod
    def get_available_providers() -> list[str]:
        """Get list of available provider types"""
        return ["openai", "azure"]
