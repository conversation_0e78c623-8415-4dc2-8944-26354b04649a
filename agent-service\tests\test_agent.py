import pytest
from httpx import AsyncClient
from main import app


@pytest.mark.asyncio
async def test_root_endpoint():
    """Test root endpoint"""
    async with Async<PERSON><PERSON>(app=app, base_url="http://test") as client:
        response = await client.get("/")
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        assert "version" in data
        assert "status" in data


@pytest.mark.asyncio
async def test_health_check():
    """Test health check endpoint"""
    async with Async<PERSON>lient(app=app, base_url="http://test") as client:
        response = await client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert "status" in data
        assert "ai_provider" in data
        assert "version" in data


@pytest.mark.asyncio
async def test_chat_without_auth():
    """Test chat endpoint without authentication"""
    async with Async<PERSON>lient(app=app, base_url="http://test") as client:
        response = await client.post(
            "/agent/chat",
            json={
                "message": "Hello, <PERSON>!"
            }
        )
        assert response.status_code == 401


@pytest.mark.asyncio
async def test_capabilities_without_auth():
    """Test capabilities endpoint without authentication"""
    async with AsyncClient(app=app, base_url="http://test") as client:
        response = await client.get("/agent/capabilities")
        assert response.status_code == 401
