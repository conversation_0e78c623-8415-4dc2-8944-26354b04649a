import pytest
from fastapi.testclient import TestClient
from main import app

client = TestClient(app)


def test_root_endpoint():
    """Test root endpoint"""
    response = client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert "message" in data
    assert "version" in data
    assert "status" in data


def test_health_check():
    """Test health check endpoint"""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert "status" in data
    assert "ai_provider" in data
    assert "version" in data


def test_chat_without_auth():
    """Test chat endpoint without authentication"""
    response = client.post(
        "/agent/chat",
        json={
            "message": "Hello, AI!"
        }
    )
    assert response.status_code == 401


def test_capabilities_without_auth():
    """Test capabilities endpoint without authentication"""
    response = client.get("/agent/capabilities")
    assert response.status_code == 401
