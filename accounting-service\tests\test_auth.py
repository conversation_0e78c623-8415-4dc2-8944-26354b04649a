import pytest


@pytest.mark.asyncio
async def test_register_user(client):
    """Test user registration"""
    response = await client.post(
        "/auth/register",
        json={
            "email": "<EMAIL>",
            "password": "testpassword123",
            "first_name": "Test",
            "last_name": "User"
        }
    )
    assert response.status_code == 200
    data = response.json()
    assert data["email"] == "<EMAIL>"
    assert data["first_name"] == "Test"
    assert "id" in data


@pytest.mark.asyncio
async def test_login_user(client):
    """Test user login"""
    # First register a user
    await client.post(
        "/auth/register",
        json={
            "email": "<EMAIL>",
            "password": "testpassword123"
        }
    )

    # Then login
    response = await client.post(
        "/auth/login",
        data={
            "username": "<EMAIL>",
            "password": "testpassword123"
        }
    )
    assert response.status_code == 200
    data = response.json()
    assert "access_token" in data
    assert data["token_type"] == "bearer"


@pytest.mark.asyncio
async def test_login_invalid_credentials(client):
    """Test login with invalid credentials"""
    response = await client.post(
        "/auth/login",
        data={
            "username": "<EMAIL>",
            "password": "wrongpassword"
        }
    )
    assert response.status_code == 401


@pytest.mark.asyncio
async def test_register_duplicate_email(client):
    """Test registering with duplicate email"""
    # Register first user
    await client.post(
        "/auth/register",
        json={
            "email": "<EMAIL>",
            "password": "password123"
        }
    )

    # Try to register with same email
    response = await client.post(
        "/auth/register",
        json={
            "email": "<EMAIL>",
            "password": "password456"
        }
    )
    assert response.status_code == 400
    assert "already registered" in response.json()["detail"]
