import pytest
from fastapi.testclient import TestClient
from main import app

client = TestClient(app)


def test_register_user():
    """Test user registration"""
    response = client.post(
        "/auth/register",
        json={
            "email": "<EMAIL>",
            "password": "testpassword123",
            "first_name": "Test",
            "last_name": "User"
        }
    )
    assert response.status_code == 200
    data = response.json()
    assert data["email"] == "<EMAIL>"
    assert data["first_name"] == "Test"
    assert "id" in data


def test_login_user():
    """Test user login"""
    # First register a user
    client.post(
        "/auth/register",
        json={
            "email": "<EMAIL>",
            "password": "testpassword123"
        }
    )
    
    # Then login
    response = client.post(
        "/auth/login",
        data={
            "username": "<EMAIL>",
            "password": "testpassword123"
        }
    )
    assert response.status_code == 200
    data = response.json()
    assert "access_token" in data
    assert data["token_type"] == "bearer"


def test_login_invalid_credentials():
    """Test login with invalid credentials"""
    response = client.post(
        "/auth/login",
        data={
            "username": "<EMAIL>",
            "password": "wrongpassword"
        }
    )
    assert response.status_code == 401
