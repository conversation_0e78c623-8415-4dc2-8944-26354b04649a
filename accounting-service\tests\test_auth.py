def test_register_user(client):
    """Test user registration"""
    response = client.post(
        "/auth/register",
        json={
            "email": "<EMAIL>",
            "password": "testpassword123",
            "first_name": "Test",
            "last_name": "User"
        }
    )
    assert response.status_code == 200
    data = response.json()
    assert data["email"] == "<EMAIL>"
    assert data["first_name"] == "Test"
    assert "id" in data


def test_login_user(client):
    """Test user login"""
    # First register a user
    client.post(
        "/auth/register",
        json={
            "email": "<EMAIL>",
            "password": "testpassword123"
        }
    )

    # Then login
    response = client.post(
        "/auth/login",
        data={
            "username": "<EMAIL>",
            "password": "testpassword123"
        }
    )
    assert response.status_code == 200
    data = response.json()
    assert "access_token" in data
    assert data["token_type"] == "bearer"


def test_login_invalid_credentials(client):
    """Test login with invalid credentials"""
    response = client.post(
        "/auth/login",
        data={
            "username": "<EMAIL>",
            "password": "wrongpassword"
        }
    )
    assert response.status_code == 401


def test_register_duplicate_email(client):
    """Test registering with duplicate email"""
    # Register first user
    client.post(
        "/auth/register",
        json={
            "email": "<EMAIL>",
            "password": "password123"
        }
    )

    # Try to register with same email
    response = client.post(
        "/auth/register",
        json={
            "email": "<EMAIL>",
            "password": "password456"
        }
    )
    assert response.status_code == 400
    assert "already registered" in response.json()["detail"]
