from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_

from database import get_db
from models import Account
from schemas.account import AccountCreate, AccountUpdate, AccountResponse
from core.dependencies import get_current_session, require_permission, SessionTokenData

router = APIRouter()


@router.post("/", response_model=AccountResponse)
async def create_account(
    account_data: AccountCreate,
    session: SessionTokenData = Depends(require_permission("accounts:create")),
    db: AsyncSession = Depends(get_db)
):
    """Create a new account - TENANT ISOLATED"""
    # Check if account number already exists for this tenant
    result = await db.execute(
        select(Account).where(
            and_(
                Account.tenant_id == session.tenant_id,
                Account.account_number == account_data.account_number
            )
        )
    )
    if result.scalar_one_or_none():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Account number {account_data.account_number} already exists"
        )
    
    # Validate parent account if specified
    if account_data.parent_account_id:
        parent_result = await db.execute(
            select(Account).where(
                and_(
                    Account.tenant_id == session.tenant_id,
                    Account.id == account_data.parent_account_id,
                    Account.is_active == True
                )
            )
        )
        if not parent_result.scalar_one_or_none():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Parent account not found or not active"
            )
    
    # Create account with MANDATORY tenant_id
    account = Account(
        tenant_id=session.tenant_id,  # CRITICAL: Always set tenant_id
        **account_data.model_dump()
    )
    
    db.add(account)
    await db.commit()
    await db.refresh(account)
    
    return account


@router.get("/", response_model=List[AccountResponse])
async def get_accounts(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    account_type: Optional[str] = Query(None),
    is_active: Optional[bool] = Query(None),
    session: SessionTokenData = Depends(require_permission("accounts:read")),
    db: AsyncSession = Depends(get_db)
):
    """Get accounts - TENANT ISOLATED"""
    query = select(Account).where(Account.tenant_id == session.tenant_id)
    
    # Apply filters
    if account_type:
        query = query.where(Account.account_type == account_type)
    if is_active is not None:
        query = query.where(Account.is_active == is_active)
    
    # Apply pagination and ordering
    query = query.order_by(Account.account_number).offset(skip).limit(limit)
    
    result = await db.execute(query)
    accounts = result.scalars().all()
    
    return accounts


@router.get("/{account_id}", response_model=AccountResponse)
async def get_account(
    account_id: int,
    session: SessionTokenData = Depends(require_permission("accounts:read")),
    db: AsyncSession = Depends(get_db)
):
    """Get specific account - TENANT ISOLATED"""
    result = await db.execute(
        select(Account).where(
            and_(
                Account.tenant_id == session.tenant_id,
                Account.id == account_id
            )
        )
    )
    account = result.scalar_one_or_none()
    
    if not account:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Account not found"
        )
    
    return account


@router.put("/{account_id}", response_model=AccountResponse)
async def update_account(
    account_id: int,
    account_update: AccountUpdate,
    session: SessionTokenData = Depends(require_permission("accounts:update")),
    db: AsyncSession = Depends(get_db)
):
    """Update account - TENANT ISOLATED"""
    # Get existing account with tenant isolation
    result = await db.execute(
        select(Account).where(
            and_(
                Account.tenant_id == session.tenant_id,
                Account.id == account_id
            )
        )
    )
    account = result.scalar_one_or_none()
    
    if not account:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Account not found"
        )
    
    # Validate parent account if being updated
    if account_update.parent_account_id is not None:
        if account_update.parent_account_id == account_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Account cannot be its own parent"
            )
        
        if account_update.parent_account_id != 0:  # 0 means remove parent
            parent_result = await db.execute(
                select(Account).where(
                    and_(
                        Account.tenant_id == session.tenant_id,
                        Account.id == account_update.parent_account_id,
                        Account.is_active == True
                    )
                )
            )
            if not parent_result.scalar_one_or_none():
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Parent account not found or not active"
                )
    
    # Update account fields
    for field, value in account_update.model_dump(exclude_unset=True).items():
        if field == "parent_account_id" and value == 0:
            value = None  # Convert 0 to None for removing parent
        setattr(account, field, value)
    
    await db.commit()
    await db.refresh(account)
    
    return account


@router.delete("/{account_id}")
async def delete_account(
    account_id: int,
    session: SessionTokenData = Depends(require_permission("accounts:delete")),
    db: AsyncSession = Depends(get_db)
):
    """Soft delete account - TENANT ISOLATED"""
    # Get existing account with tenant isolation
    result = await db.execute(
        select(Account).where(
            and_(
                Account.tenant_id == session.tenant_id,
                Account.id == account_id
            )
        )
    )
    account = result.scalar_one_or_none()
    
    if not account:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Account not found"
        )
    
    # Soft delete by setting is_active to False
    account.is_active = False
    await db.commit()
    
    return {"message": "Account deleted successfully"}
