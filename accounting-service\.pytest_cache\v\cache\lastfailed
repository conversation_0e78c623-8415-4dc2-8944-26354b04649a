{"tests/test_accounts.py::test_create_account_without_auth": true, "tests/test_accounts.py::test_get_accounts_without_auth": true, "tests/test_accounts.py::test_health_check": true, "tests/test_accounts.py::test_root_endpoint": true, "tests/test_accounts.py::test_get_user_info_without_auth": true, "tests/test_accounts.py::test_get_user_tenants_without_auth": true, "tests/test_auth.py::test_register_user": true, "tests/test_auth.py::test_login_user": true, "tests/test_auth.py::test_login_invalid_credentials": true, "tests/test_auth.py::test_register_duplicate_email": true, "tests/test_simple.py::test_health_endpoint_without_db": true, "tests/test_simple.py::test_auth_endpoints_exist": true, "tests/test_simple.py::test_protected_endpoints_require_auth": true, "tests/test_simple.py::test_accounts_crud_endpoints_exist": true, "tests/test_simple.py::test_api_documentation_endpoints": true, "tests/test_simple.py::test_cors_headers": true, "tests/test_simple.py::test_invalid_endpoints": true}