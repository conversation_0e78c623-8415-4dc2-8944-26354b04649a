from pydantic import BaseModel
from typing import Optional
from datetime import datetime


class TenantBase(BaseModel):
    """Base tenant schema"""
    company_name: str


class TenantCreate(TenantBase):
    """Tenant creation schema"""
    pass


class TenantResponse(TenantBase):
    """Tenant response schema"""
    id: int
    is_active: bool
    created_at: datetime
    
    class Config:
        from_attributes = True
