# Integration tests - Test the real running services

Write-Host "🔍 Running Integration Tests" -ForegroundColor Green
Write-Host "============================" -ForegroundColor Green

$baseUrl = "http://localhost:8000"
$agentUrl = "http://localhost:8001"
$allPassed = $true

function Test-Endpoint {
    param(
        [string]$Url,
        [string]$Description,
        [int]$ExpectedStatus = 200
    )
    
    try {
        $response = Invoke-WebRequest -Uri $Url -UseBasicParsing -TimeoutSec 10
        if ($response.StatusCode -eq $ExpectedStatus) {
            Write-Host "✅ $Description" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ $Description - Expected $ExpectedStatus, got $($response.StatusCode)" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ $Description - Error: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Test-JsonEndpoint {
    param(
        [string]$Url,
        [string]$Description,
        [string[]]$RequiredFields
    )
    
    try {
        $response = Invoke-RestMethod -Uri $Url -TimeoutSec 10
        
        $missing = @()
        foreach ($field in $RequiredFields) {
            if (-not $response.PSObject.Properties[$field]) {
                $missing += $field
            }
        }
        
        if ($missing.Count -eq 0) {
            Write-Host "✅ $Description" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ $Description - Missing fields: $($missing -join ', ')" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ $Description - Error: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Test-AuthFlow {
    Write-Host "`n🔐 Testing Authentication Flow..." -ForegroundColor Cyan
    
    # Test user registration
    $registerData = @{
        email = "<EMAIL>"
        password = "testpass123"
        first_name = "Integration"
        last_name = "Test"
    } | ConvertTo-Json
    
    try {
        $registerResponse = Invoke-RestMethod -Uri "$baseUrl/auth/register" -Method Post -Body $registerData -ContentType "application/json" -TimeoutSec 10
        Write-Host "✅ User registration works" -ForegroundColor Green
        
        # Test login
        $loginData = "username=<EMAIL>&password=testpass123"
        $loginResponse = Invoke-RestMethod -Uri "$baseUrl/auth/login" -Method Post -Body $loginData -ContentType "application/x-www-form-urlencoded" -TimeoutSec 10
        
        if ($loginResponse.access_token) {
            Write-Host "✅ User login works" -ForegroundColor Green
            return $loginResponse.access_token
        } else {
            Write-Host "❌ Login failed - No access token" -ForegroundColor Red
            return $null
        }
    } catch {
        # User might already exist, try login directly
        try {
            $loginData = "username=<EMAIL>&password=testpass123"
            $loginResponse = Invoke-RestMethod -Uri "$baseUrl/auth/login" -Method Post -Body $loginData -ContentType "application/x-www-form-urlencoded" -TimeoutSec 10
            
            if ($loginResponse.access_token) {
                Write-Host "✅ User login works (existing user)" -ForegroundColor Green
                return $loginResponse.access_token
            } else {
                Write-Host "❌ Login failed - No access token" -ForegroundColor Red
                return $null
            }
        } catch {
            Write-Host "❌ Authentication flow failed: $($_.Exception.Message)" -ForegroundColor Red
            return $null
        }
    }
}

# Test basic endpoints
Write-Host "`n🌐 Testing Basic Endpoints..." -ForegroundColor Cyan
$allPassed = $allPassed -and (Test-JsonEndpoint "$baseUrl/" "Accounting Service Root" @("message", "version", "status"))
$allPassed = $allPassed -and (Test-JsonEndpoint "$baseUrl/health" "Accounting Service Health" @("status", "database", "version"))
$allPassed = $allPassed -and (Test-JsonEndpoint "$agentUrl/" "Agent Service Root" @("message", "version", "status"))
$allPassed = $allPassed -and (Test-JsonEndpoint "$agentUrl/health" "Agent Service Health" @("status", "ai_provider", "version"))

# Test API documentation
Write-Host "`n📚 Testing API Documentation..." -ForegroundColor Cyan
$allPassed = $allPassed -and (Test-Endpoint "$baseUrl/docs" "Accounting API Docs")
$allPassed = $allPassed -and (Test-Endpoint "$agentUrl/docs" "Agent API Docs")
$allPassed = $allPassed -and (Test-JsonEndpoint "$baseUrl/openapi.json" "Accounting OpenAPI Schema" @("openapi", "info"))
$allPassed = $allPassed -and (Test-JsonEndpoint "$agentUrl/openapi.json" "Agent OpenAPI Schema" @("openapi", "info"))

# Test protected endpoints (should return 401)
Write-Host "`n🔒 Testing Protected Endpoints..." -ForegroundColor Cyan
$allPassed = $allPassed -and (Test-Endpoint "$baseUrl/accounts/" "Accounts endpoint requires auth" 401)
$allPassed = $allPassed -and (Test-Endpoint "$baseUrl/users/me" "User info requires auth" 401)
$allPassed = $allPassed -and (Test-Endpoint "$agentUrl/agent/chat" "Agent chat requires auth" 401)

# Test authentication flow
$accessToken = Test-AuthFlow

if ($accessToken) {
    Write-Host "`n🎫 Testing Authenticated Endpoints..." -ForegroundColor Cyan
    
    $headers = @{
        "Authorization" = "Bearer $accessToken"
        "Content-Type" = "application/json"
    }
    
    try {
        # Test user info
        $userInfo = Invoke-RestMethod -Uri "$baseUrl/users/me" -Headers $headers -TimeoutSec 10
        if ($userInfo.email) {
            Write-Host "✅ Authenticated user info works" -ForegroundColor Green
        } else {
            Write-Host "❌ User info missing email" -ForegroundColor Red
            $allPassed = $false
        }
        
        # Test user tenants
        $tenants = Invoke-RestMethod -Uri "$baseUrl/users/me/tenants" -Headers $headers -TimeoutSec 10
        Write-Host "✅ User tenants endpoint works (found $($tenants.Count) tenants)" -ForegroundColor Green
        
    } catch {
        Write-Host "❌ Authenticated endpoints failed: $($_.Exception.Message)" -ForegroundColor Red
        $allPassed = $false
    }
}

# Test database connectivity (via health endpoint)
Write-Host "`n🗄️  Testing Database Connectivity..." -ForegroundColor Cyan
try {
    $healthResponse = Invoke-RestMethod -Uri "$baseUrl/health" -TimeoutSec 10
    if ($healthResponse.database -eq "connected") {
        Write-Host "✅ Database connection works" -ForegroundColor Green
    } else {
        Write-Host "❌ Database connection issue: $($healthResponse.database)" -ForegroundColor Red
        $allPassed = $false
    }
} catch {
    Write-Host "❌ Database health check failed: $($_.Exception.Message)" -ForegroundColor Red
    $allPassed = $false
}

# Final summary
Write-Host "`n📋 Integration Test Summary:" -ForegroundColor Green
if ($allPassed) {
    Write-Host "🎉 All integration tests passed!" -ForegroundColor Green
    Write-Host "✅ Services are running correctly" -ForegroundColor Green
    Write-Host "✅ Database is connected" -ForegroundColor Green
    Write-Host "✅ Authentication works" -ForegroundColor Green
    Write-Host "✅ API endpoints respond correctly" -ForegroundColor Green
} else {
    Write-Host "⚠️  Some integration tests failed!" -ForegroundColor Yellow
    Write-Host "Check the details above and ensure services are running:" -ForegroundColor Yellow
    Write-Host "  docker-compose ps" -ForegroundColor Gray
    Write-Host "  docker-compose logs" -ForegroundColor Gray
}

Write-Host "`n💡 Tip: These tests verify the actual running system" -ForegroundColor Cyan
