import pytest
import asyncio
from fastapi.testclient import Test<PERSON>lient

from main import app


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="function")
def client():
    """Create test client"""
    with TestClient(app) as c:
        yield c


@pytest.fixture
async def demo_tenant_and_user(client):
    """Create demo tenant and user for testing"""
    # Register user
    user_response = await client.post(
        "/auth/register",
        json={
            "email": "<EMAIL>",
            "password": "test123",
            "first_name": "Test",
            "last_name": "User"
        }
    )
    assert user_response.status_code == 200
    user_data = user_response.json()
    
    # Login to get access token
    login_response = await client.post(
        "/auth/login",
        data={
            "username": "<EMAIL>",
            "password": "test123"
        }
    )
    assert login_response.status_code == 200
    tokens = login_response.json()
    access_token = tokens["access_token"]
    
    return {
        "user": user_data,
        "access_token": access_token,
        "headers": {"Authorization": f"Bearer {access_token}"}
    }
