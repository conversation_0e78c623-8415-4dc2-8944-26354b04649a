from pydantic import BaseModel
from typing import List, Dict, Any, Optional


class ChatMessage(BaseModel):
    """Chat message in conversation history"""
    role: str  # "user" or "assistant"
    content: str


class ChatRequest(BaseModel):
    """Chat request schema"""
    message: str
    conversation_history: Optional[List[ChatMessage]] = None


class ChatResponse(BaseModel):
    """Chat response schema"""
    response: str
    conversation_id: Optional[str] = None


class FunctionInfo(BaseModel):
    """Information about available functions"""
    name: str
    description: str
    parameters: List[str]


class AgentCapabilities(BaseModel):
    """Agent capabilities response"""
    ai_provider: str
    available_models: List[str]
    functions: List[FunctionInfo]
