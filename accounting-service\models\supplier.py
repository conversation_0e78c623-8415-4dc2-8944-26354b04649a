from sqlalchemy import Column, Inte<PERSON>, String, Bo<PERSON>an, DateTime, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from database import Base


class Supplier(Base):
    """
    Supplier model - Suppliers for each tenant
    """
    __tablename__ = "suppliers"

    id = Column(Integer, primary_key=True, index=True)
    tenant_id = Column(Integer, ForeignKey("tenants.id"), nullable=False, index=True)
    supplier_number = Column(String(20), nullable=False, index=True)
    name = Column(String(255), nullable=False, index=True)
    organization_number = Column(String(20))
    contact_person = Column(String(100))
    email = Column(String(255))
    phone = Column(String(20))
    address = Column(String(255))
    postal_code = Column(String(10))
    city = Column(String(100))
    country = Column(String(100))
    payment_terms = Column(String(50))
    is_active = Column(Boolean, default=True, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    tenant = relationship("Tenant", back_populates="suppliers")
    payables = relationship("Payable", back_populates="supplier")

    def __repr__(self):
        return f"<Supplier(id={self.id}, tenant_id={self.tenant_id}, name='{self.name}')>"
