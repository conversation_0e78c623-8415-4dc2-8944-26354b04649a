# API Examples

Detta dokument visar exempel på hur man använder AI Accounting System API:et.

## Autentisering

### 1. Registrera användare

```bash
curl -X POST "http://localhost:8000/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "securepassword123",
    "first_name": "<PERSON>",
    "last_name": "<PERSON><PERSON>"
  }'
```

### 2. <PERSON><PERSON><PERSON> in

```bash
curl -X POST "http://localhost:8000/auth/login" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=<EMAIL>&password=securepassword123"
```

Svar:
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer"
}
```

### 3. <PERSON>ämta tillgängliga tenants

```bash
curl -X GET "http://localhost:8000/users/me/tenants" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 4. Skapa session för specifik tenant

```bash
curl -X POST "http://localhost:8000/auth/session" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "tenant_id": 1
  }'
```

Svar:
```json
{
  "session_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "tenant_id": 1,
  "tenant_name": "Demo Företag AB",
  "permissions": ["accounts:read", "accounts:create", ...]
}
```

## Kontoplan (Accounts)

### Hämta alla konton

```bash
curl -X GET "http://localhost:8000/accounts/" \
  -H "Authorization: Bearer YOUR_SESSION_TOKEN"
```

### Hämta konton med filter

```bash
curl -X GET "http://localhost:8000/accounts/?account_type=ASSET&is_active=true&limit=50" \
  -H "Authorization: Bearer YOUR_SESSION_TOKEN"
```

### Skapa nytt konto

```bash
curl -X POST "http://localhost:8000/accounts/" \
  -H "Authorization: Bearer YOUR_SESSION_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "account_number": 1234,
    "name": "Nytt tillgångskonto",
    "account_type": "ASSET"
  }'
```

### Uppdatera konto

```bash
curl -X PUT "http://localhost:8000/accounts/123" \
  -H "Authorization: Bearer YOUR_SESSION_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Uppdaterat kontonamn",
    "is_active": true
  }'
```

### Ta bort konto (soft delete)

```bash
curl -X DELETE "http://localhost:8000/accounts/123" \
  -H "Authorization: Bearer YOUR_SESSION_TOKEN"
```

## AI Agent

### Chatta med AI-agenten

```bash
curl -X POST "http://localhost:8001/agent/chat" \
  -H "Authorization: Bearer YOUR_SESSION_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Visa mig alla tillgångskonton",
    "conversation_history": [
      {
        "role": "user",
        "content": "Hej, jag behöver hjälp med kontoplanen"
      },
      {
        "role": "assistant", 
        "content": "Hej! Jag hjälper gärna till med kontoplanen. Vad vill du göra?"
      }
    ]
  }'
```

### Hämta AI-agentens kapaciteter

```bash
curl -X GET "http://localhost:8001/agent/capabilities" \
  -H "Authorization: Bearer YOUR_SESSION_TOKEN"
```

## Exempel på komplett flöde

### 1. Registrera och logga in

```bash
# Registrera
curl -X POST "http://localhost:8000/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "demo123",
    "first_name": "Demo",
    "last_name": "User"
  }'

# Logga in
ACCESS_TOKEN=$(curl -s -X POST "http://localhost:8000/auth/login" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=<EMAIL>&password=demo123" | jq -r '.access_token')

echo "Access token: $ACCESS_TOKEN"
```

### 2. Välj tenant och skapa session

```bash
# Hämta tenants
curl -X GET "http://localhost:8000/users/me/tenants" \
  -H "Authorization: Bearer $ACCESS_TOKEN"

# Skapa session för tenant 1
SESSION_TOKEN=$(curl -s -X POST "http://localhost:8000/auth/session" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"tenant_id": 1}' | jq -r '.session_token')

echo "Session token: $SESSION_TOKEN"
```

### 3. Använd API:et

```bash
# Hämta konton
curl -X GET "http://localhost:8000/accounts/" \
  -H "Authorization: Bearer $SESSION_TOKEN"

# Chatta med AI
curl -X POST "http://localhost:8001/agent/chat" \
  -H "Authorization: Bearer $SESSION_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Skapa ett nytt konto 1500 som heter Kundfordringar och är av typen ASSET"
  }'
```

## Felhantering

API:et returnerar standardiserade HTTP-statuskoder:

- `200` - Framgång
- `201` - Skapad
- `400` - Felaktig förfrågan
- `401` - Ej autentiserad
- `403` - Ej behörig
- `404` - Ej hittad
- `422` - Valideringsfel
- `500` - Serverfel

Exempel på felmeddelande:
```json
{
  "detail": "Permission 'accounts:create' required"
}
```
