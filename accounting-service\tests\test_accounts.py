def test_create_account_without_auth(client):
    """Test creating account without authentication"""
    response = client.post(
        "/accounts/",
        json={
            "account_number": 1000,
            "name": "Test Account",
            "account_type": "ASSET"
        }
    )
    assert response.status_code == 401


def test_get_accounts_without_auth(client):
    """Test getting accounts without authentication"""
    response = client.get("/accounts/")
    assert response.status_code == 401


def test_health_check(client):
    """Test health check endpoint"""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"
    assert "database" in data
    assert "version" in data


def test_root_endpoint(client):
    """Test root endpoint"""
    response = client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert "message" in data
    assert "version" in data
    assert "status" in data


def test_get_user_info_without_auth(client):
    """Test getting user info without authentication"""
    response = client.get("/users/me")
    assert response.status_code == 401


def test_get_user_tenants_without_auth(client):
    """Test getting user tenants without authentication"""
    response = client.get("/users/me/tenants")
    assert response.status_code == 401
