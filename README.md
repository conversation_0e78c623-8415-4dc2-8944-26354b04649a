# AI-Orkestrerat Multi-Tenant Redovisningssystem

Ett modernt, molnbaserat och AI-orkestrerat redovisningssystem byggt som en multi-tenant SaaS-plattform.

## 🏗️ Arkitektur

- **API-First:** All funktionalitet exponeras via RESTful API
- **Multi-Tenant:** Strikt dataisolering mellan kunder via tenant_id
- **Mikrotjänster:** Containeriserad arkitektur med Docker
- **AI-Orkestrering:** Flexibel LLM-integration med Strategy Pattern

## 🛠️ Teknikstack

- **Backend:** Python 3.12 med FastAPI
- **Databas:** PostgreSQL 15+ med SQLAlchemy 2.0
- **Autentisering:** JWT-baserad stateless autentisering
- **AI:** OpenAI/Azure OpenAI med flexibelt abstraktionslager
- **Containerisering:** Docker & docker-compose

## 📁 Projektstruktur

```
├── accounting-service/     # Kärnredovisningssystem
│   ├── models/            # SQLAlchemy databasmodeller
│   ├── routers/           # FastAPI endpoints
│   ├── core/              # Säkerhet och konfiguration
│   ├── schemas/           # Pydantic scheman
│   └── tests/             # Enhetstester
├── agent-service/         # AI-agent för interaktion
│   ├── ai/                # AI-abstraktionslager
│   ├── services/          # Affärslogik
│   └── routers/           # API endpoints
├── scripts/               # Databas-seeding och verktyg
├── docker-compose.yml     # Lokal utvecklingsmiljö
└── Makefile              # Utvecklingskommandon
```

## 🚀 Snabbstart

### 1. Förberedelser

```bash
# Klona projektet
git clone <repository-url>
cd ai_books

# Kopiera miljövariabler
cp .env.example .env

# Redigera .env med dina API-nycklar
nano .env
```

### 2. Starta systemet

```bash
# Bygg och starta alla tjänster
make reset

# Eller manuellt:
docker-compose build
docker-compose up -d
sleep 10
python scripts/seed_data.py
```

### 3. Testa systemet

```bash
# Öppna API-dokumentation
open http://localhost:8000/docs  # Accounting Service
open http://localhost:8001/docs  # Agent Service

# Eller använd demo-användare:
# Admin: <EMAIL> / admin123
# Bokförare: <EMAIL> / bokforare123
```

## 📚 API-användning

Se [API_EXAMPLES.md](API_EXAMPLES.md) för detaljerade exempel.

### Grundläggande flöde:

1. **Registrera/Logga in** → Få access_token
2. **Välj tenant** → Få session_token med behörigheter
3. **Använd API:et** med session_token
4. **Chatta med AI** för intelligent assistans

## 🔒 Säkerhet

- **Strikt tenant-isolering:** Alla databasoperationer filtreras på tenant_id
- **RBAC:** Rollbaserad behörighetskontroll med granulära permissions
- **JWT-tokens:** Stateless autentisering med korta livslängder
- **Middleware:** Automatisk tenant-validering på alla endpoints

## 🧪 Utveckling

```bash
# Installera dependencies lokalt
make install-deps

# Kör tester
make test

# Visa loggar
make logs

# Databas-shell
make db-shell

# Backup databas
make db-backup
```

## 🤖 AI-funktioner

Systemet inkluderar en intelligent AI-agent som kan:

- Svara på frågor om redovisning
- Skapa och hantera konton
- Analysera finansiell data
- Ge råd enligt svenska redovisningsstandarder (BAS)

AI-agenten stöder både OpenAI och Azure OpenAI via ett flexibelt abstraktionslager.

## 📊 Databasschema

Systemet implementerar ett komplett multi-tenant redovisningsschema:

- **Tenants:** Företag/organisationer
- **Users & Roles:** Användare med rollbaserade behörigheter
- **Accounts:** Kontoplan (BAS 2024)
- **Vouchers:** Verifikationer och transaktioner
- **Suppliers/Customers:** Leverantörer och kunder
- **Financial Years:** Räkenskapsår
- **VAT Reports:** Momsrapporter

## 🔧 Konfiguration

Viktiga miljövariabler i `.env`:

```bash
# Databas
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/ai_accounting

# JWT
JWT_SECRET_KEY=your-super-secret-key

# AI (välj en)
OPENAI_API_KEY=your-openai-key
# ELLER
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_API_KEY=your-azure-key
```

## 📈 Nästa steg

Detta är en grundimplementation. Föreslagna utökningar:

1. **Fler AI-funktioner:** Automatisk kategorisering, OCR för fakturor
2. **Rapporter:** Balans- och resultatrapporter
3. **Integrationer:** Bankfiler, e-fakturering
4. **Frontend:** React/Vue.js webbapplikation
5. **Deployment:** Kubernetes, CI/CD pipelines

## 🆘 Felsökning

```bash
# Kontrollera tjänsternas status
docker-compose ps

# Visa loggar för specifik tjänst
make logs-accounting
make logs-agent

# Återställ allt
make clean
make reset
```

## 📄 Licens

Detta projekt är utvecklat som en demonstration av modern arkitektur för AI-orkestrerade affärssystem.
