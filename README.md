# AI-Orkestrerat Multi-Tenant Redovisningssystem

Ett modernt, molnbaserat och AI-orkestrerat redovisningssystem byggt som en multi-tenant SaaS-plattform.

## Arkitektur

- **API-First:** All funktionalitet exponeras via RESTful API
- **Multi-Tenant:** Strikt dataisolering mellan kunder via tenant_id
- **Mikrotjänster:** Containeriserad arkitektur med Docker
- **AI-Orkestrering:** Flexibel LLM-integration med Strategy Pattern

## Teknikstack

- **Backend:** Python 3.12 med FastAPI
- **Databas:** PostgreSQL 15+ med SQLAlchemy 2.0
- **Autentisering:** JWT-baserad stateless autentisering
- **Containerisering:** Docker & docker-compose

## Projektstruktur

```
├── accounting-service/     # Kärnredovisningssystem
├── agent-service/         # AI-agent för interaktion
├── shared/               # Delade komponenter
├── docker-compose.yml    # Lokal utvecklingsmiljö
└── scripts/             # Databas-seeding och verktyg
```

## Snabbstart

```bash
# Starta utvecklingsmiljön
docker-compose up -d

# Kör databas-migrations
cd accounting-service
python -m alembic upgrade head

# Seed grunddata
python scripts/seed_data.py
```

## Säkerhet

- Strikt tenant-isolering på databasnivå
- RBAC-baserad behörighetskontroll
- JWT-autentisering med korta livslängder
- Obligatorisk tenant_id-filtrering på alla operationer
