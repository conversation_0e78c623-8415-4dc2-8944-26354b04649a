# Database Configuration
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/ai_accounting

# JWT Configuration
JWT_SECRET_KEY=your-super-secret-jwt-key-change-in-production
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_SESSION_TOKEN_EXPIRE_MINUTES=480

# AI Configuration
OPENAI_API_KEY=your-openai-api-key
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_API_KEY=your-azure-openai-key
AZURE_OPENAI_API_VERSION=2024-02-15-preview

# Service URLs
ACCOUNTING_SERVICE_URL=http://localhost:8000
AGENT_SERVICE_URL=http://localhost:8001
