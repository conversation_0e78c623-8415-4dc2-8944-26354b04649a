from pydantic import BaseModel
from typing import Optional
from decimal import Decimal
from datetime import datetime


class AccountBase(BaseModel):
    """Base account schema"""
    account_number: int
    name: str
    account_type: str  # ASSET, LIABILITY, EQUITY, REVENUE, EXPENSE
    parent_account_id: Optional[int] = None


class AccountCreate(AccountBase):
    """Account creation schema"""
    pass


class AccountUpdate(BaseModel):
    """Account update schema"""
    name: Optional[str] = None
    account_type: Optional[str] = None
    parent_account_id: Optional[int] = None
    is_active: Optional[bool] = None


class AccountResponse(AccountBase):
    """Account response schema"""
    id: int
    tenant_id: int
    is_active: bool
    balance: Decimal
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True
