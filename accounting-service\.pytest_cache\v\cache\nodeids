["tests/test_accounts.py::test_create_account_without_auth", "tests/test_accounts.py::test_get_accounts_without_auth", "tests/test_accounts.py::test_get_user_info_without_auth", "tests/test_accounts.py::test_get_user_tenants_without_auth", "tests/test_accounts.py::test_health_check", "tests/test_accounts.py::test_root_endpoint", "tests/test_auth.py::test_login_invalid_credentials", "tests/test_auth.py::test_login_user", "tests/test_auth.py::test_register_duplicate_email", "tests/test_auth.py::test_register_user", "tests/test_simple.py::test_accounts_crud_endpoints_exist", "tests/test_simple.py::test_api_documentation_endpoints", "tests/test_simple.py::test_auth_endpoints_exist", "tests/test_simple.py::test_cors_headers", "tests/test_simple.py::test_health_endpoint_without_db", "tests/test_simple.py::test_invalid_endpoints", "tests/test_simple.py::test_protected_endpoints_require_auth", "tests/test_simple.py::test_root_endpoint"]