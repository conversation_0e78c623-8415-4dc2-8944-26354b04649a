# Simple system integration tests

Write-Host "Testing AI Accounting System" -ForegroundColor Green
Write-Host "============================" -ForegroundColor Green

$accountingUrl = "http://localhost:8000"
$agentUrl = "http://localhost:8001"
$testsPassed = 0
$testsTotal = 0

function Test-Endpoint {
    param([string]$Url, [string]$Name)
    
    $script:testsTotal++
    Write-Host "Testing $Name..." -ForegroundColor Cyan
    
    try {
        $response = Invoke-WebRequest -Uri $Url -UseBasicParsing -TimeoutSec 5
        if ($response.StatusCode -eq 200) {
            Write-Host "  PASS - $Name" -ForegroundColor Green
            $script:testsPassed++
            return $true
        } else {
            Write-Host "  FAIL - $Name (Status: $($response.StatusCode))" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "  FAIL - $Name (Error: $($_.Exception.Message))" -ForegroundColor Red
        return $false
    }
}

function Test-JsonResponse {
    param([string]$Url, [string]$Name, [string[]]$RequiredFields)
    
    $script:testsTotal++
    Write-Host "Testing $Name..." -ForegroundColor Cyan
    
    try {
        $response = Invoke-RestMethod -Uri $Url -TimeoutSec 5
        
        $missing = @()
        foreach ($field in $RequiredFields) {
            if (-not $response.PSObject.Properties[$field]) {
                $missing += $field
            }
        }
        
        if ($missing.Count -eq 0) {
            Write-Host "  PASS - $Name" -ForegroundColor Green
            $script:testsPassed++
            return $true
        } else {
            Write-Host "  FAIL - $Name (Missing: $($missing -join ', '))" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "  FAIL - $Name (Error: $($_.Exception.Message))" -ForegroundColor Red
        return $false
    }
}

function Test-AuthRequired {
    param([string]$Url, [string]$Name)
    
    $script:testsTotal++
    Write-Host "Testing $Name..." -ForegroundColor Cyan
    
    try {
        $response = Invoke-WebRequest -Uri $Url -UseBasicParsing -TimeoutSec 5
        Write-Host "  FAIL - $Name (Should require auth but got $($response.StatusCode))" -ForegroundColor Red
        return $false
    } catch {
        if ($_.Exception.Response.StatusCode -eq 401) {
            Write-Host "  PASS - $Name (Correctly requires auth)" -ForegroundColor Green
            $script:testsPassed++
            return $true
        } else {
            Write-Host "  FAIL - $Name (Wrong error: $($_.Exception.Message))" -ForegroundColor Red
            return $false
        }
    }
}

# Test basic endpoints
Write-Host "`nTesting Basic Endpoints:" -ForegroundColor Yellow
Test-JsonResponse "$accountingUrl/" "Accounting Root" @("message", "status")
Test-JsonResponse "$accountingUrl/health" "Accounting Health" @("status", "database")
Test-JsonResponse "$agentUrl/" "Agent Root" @("message", "status")
Test-JsonResponse "$agentUrl/health" "Agent Health" @("status", "ai_provider")

# Test API documentation
Write-Host "`nTesting API Documentation:" -ForegroundColor Yellow
Test-Endpoint "$accountingUrl/docs" "Accounting API Docs"
Test-Endpoint "$agentUrl/docs" "Agent API Docs"
Test-JsonResponse "$accountingUrl/openapi.json" "Accounting OpenAPI" @("openapi", "info")
Test-JsonResponse "$agentUrl/openapi.json" "Agent OpenAPI" @("openapi", "info")

# Test protected endpoints
Write-Host "`nTesting Security:" -ForegroundColor Yellow
Test-AuthRequired "$accountingUrl/accounts/" "Accounts Protection"
Test-AuthRequired "$accountingUrl/users/me" "User Info Protection"
Test-AuthRequired "$agentUrl/agent/chat" "Agent Chat Protection"

# Test authentication flow
Write-Host "`nTesting Authentication:" -ForegroundColor Yellow
$testsTotal++
Write-Host "Testing User Registration..." -ForegroundColor Cyan

try {
    $registerData = @{
        email = "test-$(Get-Random)@example.com"
        password = "testpass123"
        first_name = "Test"
        last_name = "User"
    } | ConvertTo-Json
    
    $registerResponse = Invoke-RestMethod -Uri "$accountingUrl/auth/register" -Method Post -Body $registerData -ContentType "application/json" -TimeoutSec 5
    
    if ($registerResponse.email) {
        Write-Host "  PASS - User Registration" -ForegroundColor Green
        $testsPassed++
        
        # Test login
        $testsTotal++
        Write-Host "Testing User Login..." -ForegroundColor Cyan
        
        $loginData = "username=$($registerResponse.email)&password=testpass123"
        $loginResponse = Invoke-RestMethod -Uri "$accountingUrl/auth/login" -Method Post -Body $loginData -ContentType "application/x-www-form-urlencoded" -TimeoutSec 5
        
        if ($loginResponse.access_token) {
            Write-Host "  PASS - User Login" -ForegroundColor Green
            $testsPassed++
            
            # Test authenticated endpoint
            $testsTotal++
            Write-Host "Testing Authenticated Access..." -ForegroundColor Cyan
            
            $headers = @{ "Authorization" = "Bearer $($loginResponse.access_token)" }
            $userInfo = Invoke-RestMethod -Uri "$accountingUrl/users/me" -Headers $headers -TimeoutSec 5
            
            if ($userInfo.email) {
                Write-Host "  PASS - Authenticated Access" -ForegroundColor Green
                $testsPassed++
            } else {
                Write-Host "  FAIL - Authenticated Access (No user data)" -ForegroundColor Red
            }
        } else {
            Write-Host "  FAIL - User Login (No token)" -ForegroundColor Red
        }
    } else {
        Write-Host "  FAIL - User Registration (No email returned)" -ForegroundColor Red
    }
} catch {
    Write-Host "  FAIL - Authentication Flow ($($_.Exception.Message))" -ForegroundColor Red
}

# Summary
Write-Host "`nTest Results:" -ForegroundColor Green
Write-Host "=============" -ForegroundColor Green
Write-Host "Passed: $testsPassed / $testsTotal" -ForegroundColor $(if ($testsPassed -eq $testsTotal) { "Green" } else { "Yellow" })

if ($testsPassed -eq $testsTotal) {
    Write-Host "`nAll tests passed! System is working correctly." -ForegroundColor Green
    Write-Host "Database connection: OK" -ForegroundColor Green
    Write-Host "Authentication: OK" -ForegroundColor Green
    Write-Host "API endpoints: OK" -ForegroundColor Green
} else {
    Write-Host "`nSome tests failed. Check the details above." -ForegroundColor Yellow
    Write-Host "You may need to:" -ForegroundColor Yellow
    Write-Host "  1. Check if services are running: docker-compose ps" -ForegroundColor Gray
    Write-Host "  2. Check logs: docker-compose logs" -ForegroundColor Gray
    Write-Host "  3. Restart services: .\setup.ps1 down && .\setup.ps1 up" -ForegroundColor Gray
}
