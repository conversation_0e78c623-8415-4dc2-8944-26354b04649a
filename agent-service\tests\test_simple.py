"""
Simple tests for agent service
"""
import pytest
from fastapi.testclient import Test<PERSON><PERSON>
from unittest.mock import patch

from main import app


@pytest.fixture
def client():
    """Create test client"""
    with TestClient(app) as c:
        yield c


def test_root_endpoint(client):
    """Test root endpoint"""
    response = client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert "message" in data
    assert "version" in data
    assert "status" in data


def test_health_endpoint(client):
    """Test health endpoint"""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert "status" in data
    assert "ai_provider" in data
    assert "version" in data


def test_agent_endpoints_require_auth(client):
    """Test that agent endpoints require authentication"""
    # Test chat endpoint
    response = client.post("/agent/chat", json={
        "message": "Hello, AI!"
    })
    assert response.status_code == 401
    
    # Test capabilities endpoint
    response = client.get("/agent/capabilities")
    assert response.status_code == 401


def test_invalid_chat_request(client):
    """Test chat endpoint with invalid data"""
    # Missing message field
    response = client.post("/agent/chat", json={})
    assert response.status_code in [401, 422]  # Auth error or validation error
    
    # Invalid JSON structure
    response = client.post("/agent/chat", json={
        "invalid_field": "test"
    })
    assert response.status_code in [401, 422]  # Auth error or validation error


def test_api_documentation_endpoints(client):
    """Test that API documentation endpoints work"""
    # Test OpenAPI schema
    response = client.get("/openapi.json")
    assert response.status_code == 200
    
    # Test docs redirect
    response = client.get("/docs", allow_redirects=False)
    assert response.status_code in [200, 307]  # OK or redirect
    
    # Test redoc
    response = client.get("/redoc", allow_redirects=False)
    assert response.status_code in [200, 307]  # OK or redirect


def test_cors_headers(client):
    """Test that CORS headers are present"""
    response = client.options("/")
    # Should have CORS headers or at least not fail
    assert response.status_code in [200, 405]  # OK or Method Not Allowed


def test_invalid_endpoints(client):
    """Test that invalid endpoints return 404"""
    response = client.get("/nonexistent")
    assert response.status_code == 404
    
    response = client.post("/invalid/endpoint")
    assert response.status_code == 404


def test_agent_router_structure(client):
    """Test that agent router is properly mounted"""
    # These should all return 401 (auth required) not 404 (not found)
    response = client.post("/agent/chat", json={"message": "test"})
    assert response.status_code == 401
    
    response = client.get("/agent/capabilities")
    assert response.status_code == 401
    
    # Invalid agent endpoint should return 404
    response = client.get("/agent/invalid")
    assert response.status_code == 404
