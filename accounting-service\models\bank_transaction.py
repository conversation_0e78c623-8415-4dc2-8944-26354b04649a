from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Numeric, Date, Boolean
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from database import Base


class BankTransaction(Base):
    """
    BankTransaction model - Bank transactions for each tenant
    """
    __tablename__ = "bank_transactions"

    id = Column(Integer, primary_key=True, index=True)
    tenant_id = Column(Integer, ForeignKey("tenants.id"), nullable=False, index=True)
    transaction_date = Column(Date, nullable=False, index=True)
    amount = Column(Numeric(15, 2), nullable=False)
    currency = Column(String(3), default="SEK", nullable=False)
    description = Column(String(500), nullable=False)
    reference = Column(String(100))
    bank_reference = Column(String(100))
    account_number = Column(String(50))
    is_reconciled = Column(Boolean, default=False, nullable=False)
    voucher_id = Column(Integer, ForeignKey("vouchers.id"), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    tenant = relationship("Tenant", back_populates="bank_transactions")
    voucher = relationship("Voucher")

    def __repr__(self):
        return f"<BankTransaction(id={self.id}, tenant_id={self.tenant_id}, amount={self.amount})>"
