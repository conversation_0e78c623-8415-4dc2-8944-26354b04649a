from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Numeric, Date, Boolean
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from database import Base


class Payable(Base):
    """
    Payable model - Accounts payable for each tenant
    """
    __tablename__ = "payables"

    id = Column(Integer, primary_key=True, index=True)
    tenant_id = Column(Integer, ForeignKey("tenants.id"), nullable=False, index=True)
    supplier_id = Column(Integer, ForeignKey("suppliers.id"), nullable=False, index=True)
    invoice_number = Column(String(50), nullable=False, index=True)
    invoice_date = Column(Date, nullable=False)
    due_date = Column(Date, nullable=False, index=True)
    amount = Column(Numeric(15, 2), nullable=False)
    paid_amount = Column(Numeric(15, 2), default=0, nullable=False)
    currency = Column(String(3), default="SEK", nullable=False)
    description = Column(String(500))
    reference = Column(String(100))
    is_paid = Column(Boolean, default=False, nullable=False)
    payment_date = Column(Date)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    tenant = relationship("Tenant", back_populates="payables")
    supplier = relationship("Supplier", back_populates="payables")

    def __repr__(self):
        return f"<Payable(id={self.id}, tenant_id={self.tenant_id}, invoice_number='{self.invoice_number}')>"
