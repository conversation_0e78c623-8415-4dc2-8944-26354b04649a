from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, status
from schemas.agent import ChatRequest, ChatResponse, AgentCapabilities
from services.agent_service import AgentService
from core.auth import verify_session_token

router = APIRouter()


@router.post("/chat", response_model=ChatResponse)
async def chat_with_agent(
    request: ChatRequest,
    session_token: str = Depends(verify_session_token)
):
    """Chat with the AI agent"""
    try:
        agent = AgentService(session_token)
        
        # Convert conversation history to the format expected by AgentService
        conversation_history = []
        if request.conversation_history:
            conversation_history = [
                {"role": msg.role, "content": msg.content}
                for msg in request.conversation_history
            ]
        
        response = await agent.chat(request.message, conversation_history)
        
        return ChatResponse(response=response)
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Agent error: {str(e)}"
        )


@router.get("/capabilities", response_model=AgentCapabilities)
async def get_agent_capabilities(
    session_token: str = Depends(verify_session_token)
):
    """Get agent capabilities and available functions"""
    try:
        agent = AgentService(session_token)
        capabilities = await agent.get_capabilities()
        
        return AgentCapabilities(**capabilities)
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting capabilities: {str(e)}"
        )
