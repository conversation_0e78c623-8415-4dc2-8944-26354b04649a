from sqlalchemy import <PERSON>umn, Inte<PERSON>, String, DateTime, <PERSON><PERSON><PERSON>, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from database import Base


class User(Base):
    """
    User model - represents a global user that can belong to multiple tenants
    """
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    email = Column(String(255), unique=True, nullable=False, index=True)
    password_hash = Column(String(255), nullable=False)
    first_name = Column(String(100))
    last_name = Column(String(100))
    is_active = Column(Boolean, default=True, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    tenants = relationship("UserTenant", back_populates="user")

    def __repr__(self):
        return f"<User(id={self.id}, email='{self.email}')>"


class UserTenant(Base):
    """
    UserTenant model - represents a user's membership in a tenant with a specific role
    """
    __tablename__ = "user_tenants"

    user_id = Column(Integer, ForeignKey("users.id"), primary_key=True)
    tenant_id = Column(Integer, ForeignKey("tenants.id"), primary_key=True)
    role_id = Column(Integer, ForeignKey("roles.id"), nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    user = relationship("User", back_populates="tenants")
    tenant = relationship("Tenant", back_populates="users")
    role = relationship("Role")

    def __repr__(self):
        return f"<UserTenant(user_id={self.user_id}, tenant_id={self.tenant_id}, role_id={self.role_id})>"
