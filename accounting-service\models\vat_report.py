from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Numeric, Date, Boolean, JSON
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from database import Base


class VatReport(Base):
    """
    VatReport model - VAT reports for each tenant
    """
    __tablename__ = "vat_reports"

    id = Column(Integer, primary_key=True, index=True)
    tenant_id = Column(Integer, ForeignKey("tenants.id"), nullable=False, index=True)
    period_start = Column(Date, nullable=False, index=True)
    period_end = Column(Date, nullable=False, index=True)
    report_type = Column(String(20), nullable=False)  # MONTHLY, QUARTERLY
    total_sales = Column(Numeric(15, 2), default=0, nullable=False)
    total_purchases = Column(Numeric(15, 2), default=0, nullable=False)
    vat_to_pay = Column(Numeric(15, 2), default=0, nullable=False)
    vat_to_reclaim = Column(Numeric(15, 2), default=0, nullable=False)
    net_vat = Column(Numeric(15, 2), default=0, nullable=False)
    report_data = Column(JSON)  # Detailed breakdown
    is_submitted = Column(Boolean, default=False, nullable=False)
    submitted_date = Column(DateTime(timezone=True))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    tenant = relationship("Tenant", back_populates="vat_reports")

    def __repr__(self):
        return f"<VatReport(id={self.id}, tenant_id={self.tenant_id}, period={self.period_start}-{self.period_end})>"
