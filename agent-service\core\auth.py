from fastapi import HTT<PERSON><PERSON>x<PERSON>, status, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from jose import JWTError, jwt

security = HTTPBearer()

# These should match the accounting service settings
JWT_SECRET_KEY = "your-super-secret-jwt-key-change-in-production"
JWT_ALGORITHM = "HS256"


def verify_session_token(credentials: HTTPAuthorizationCredentials = Depends(security)) -> str:
    """Verify session token and return it for forwarding to accounting service"""
    try:
        # Decode token to verify it's valid
        payload = jwt.decode(credentials.credentials, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])
        
        # Verify it's a session token
        if payload.get("type") != "session":
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token type"
            )
        
        # Return the token for forwarding to accounting service
        return credentials.credentials
        
    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials"
        )
