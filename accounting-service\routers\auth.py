from datetime import <PERSON><PERSON><PERSON>
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from database import get_db
from models import User, <PERSON>r<PERSON><PERSON><PERSON>, Tenant, Role
from schemas.auth import User<PERSON><PERSON><PERSON>, UserRegister, Token, SessionToken, TenantSelection
from schemas.user import UserResponse
from core.security import (
    verify_password, 
    get_password_hash, 
    create_access_token, 
    create_session_token
)
from core.dependencies import get_current_user, get_user_permissions
from core.config import settings

router = APIRouter()


@router.post("/register", response_model=UserResponse)
async def register(
    user_data: UserRegister,
    db: AsyncSession = Depends(get_db)
):
    """Register a new user"""
    # Check if user already exists
    result = await db.execute(select(User).where(User.email == user_data.email))
    if result.scalar_one_or_none():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered"
        )
    
    # Create new user
    hashed_password = get_password_hash(user_data.password)
    user = User(
        email=user_data.email,
        password_hash=hashed_password,
        first_name=user_data.first_name,
        last_name=user_data.last_name
    )
    
    db.add(user)
    await db.commit()
    await db.refresh(user)
    
    return user


@router.post("/login", response_model=Token)
async def login(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: AsyncSession = Depends(get_db)
):
    """Login user and return access token"""
    # Get user by email
    result = await db.execute(
        select(User).where(User.email == form_data.username, User.is_active == True)
    )
    user = result.scalar_one_or_none()
    
    if not user or not verify_password(form_data.password, user.password_hash):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password"
        )
    
    # Create access token
    access_token_expires = timedelta(minutes=settings.jwt_access_token_expire_minutes)
    access_token = create_access_token(
        data={"sub": str(user.id), "email": user.email},
        expires_delta=access_token_expires
    )
    
    return {"access_token": access_token, "token_type": "bearer"}


@router.post("/session", response_model=SessionToken)
async def create_session(
    tenant_selection: TenantSelection,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Create a session token for a specific tenant"""
    # Verify user has access to the tenant
    result = await db.execute(
        select(UserTenant, Tenant, Role)
        .join(Tenant, UserTenant.tenant_id == Tenant.id)
        .join(Role, UserTenant.role_id == Role.id)
        .where(
            UserTenant.user_id == current_user.id,
            UserTenant.tenant_id == tenant_selection.tenant_id,
            UserTenant.is_active == True,
            Tenant.is_active == True
        )
    )
    user_tenant_data = result.first()
    
    if not user_tenant_data:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access to tenant not allowed"
        )
    
    user_tenant, tenant, role = user_tenant_data
    
    # Get user permissions for this tenant
    permissions = await get_user_permissions(current_user.id, tenant_selection.tenant_id, db)
    
    # Create session token
    session_token_expires = timedelta(minutes=settings.jwt_session_token_expire_minutes)
    session_token = create_session_token(
        data={
            "user_id": current_user.id,
            "email": current_user.email,
            "tenant_id": tenant_selection.tenant_id,
            "permissions": permissions
        },
        expires_delta=session_token_expires
    )
    
    return {
        "session_token": session_token,
        "token_type": "bearer",
        "tenant_id": tenant.id,
        "tenant_name": tenant.company_name,
        "permissions": permissions
    }
