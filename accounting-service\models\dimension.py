from sqlalchemy import Column, Inte<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, DateTime, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from database import Base


class Dimension(Base):
    """
    Dimension model - Cost centers, projects, departments etc. for each tenant
    """
    __tablename__ = "dimensions"

    id = Column(Integer, primary_key=True, index=True)
    tenant_id = Column(Integer, ForeignKey("tenants.id"), nullable=False, index=True)
    dimension_type = Column(String(50), nullable=False)  # COST_CENTER, PROJECT, DEPARTMENT
    code = Column(String(20), nullable=False, index=True)
    name = Column(String(255), nullable=False)
    description = Column(String(500))
    is_active = Column(Boolean, default=True, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    tenant = relationship("Tenant", back_populates="dimensions")

    def __repr__(self):
        return f"<Dimension(id={self.id}, tenant_id={self.tenant_id}, type='{self.dimension_type}', code='{self.code}')>"
