from typing import List, Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from database import get_db
from models import User, UserTenant, Role, RolePermission
from core.security import verify_token, TokenData, SessionTokenData

security = HTTPBearer()


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_db)
) -> User:
    """Get current user from access token"""
    payload = verify_token(credentials.credentials, "access")
    
    user_id = payload.get("sub")
    if user_id is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials"
        )
    
    # Get user from database
    result = await db.execute(select(User).where(User.id == int(user_id), User.is_active == True))
    user = result.scalar_one_or_none()
    
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found"
        )
    
    return user


async def get_current_session(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_db)
) -> SessionTokenData:
    """Get current session with tenant context from session token"""
    payload = verify_token(credentials.credentials, "session")
    
    user_id = payload.get("user_id")
    tenant_id = payload.get("tenant_id")
    permissions = payload.get("permissions", [])
    email = payload.get("email")
    
    if not all([user_id, tenant_id, email]):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid session token"
        )
    
    # Verify user and tenant relationship still exists and is active
    result = await db.execute(
        select(UserTenant)
        .where(
            UserTenant.user_id == user_id,
            UserTenant.tenant_id == tenant_id,
            UserTenant.is_active == True
        )
    )
    user_tenant = result.scalar_one_or_none()
    
    if user_tenant is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Session no longer valid"
        )
    
    return SessionTokenData(
        user_id=user_id,
        email=email,
        tenant_id=tenant_id,
        permissions=permissions
    )


def require_permission(permission: str):
    """Dependency factory for permission-based access control"""
    async def permission_checker(
        session: SessionTokenData = Depends(get_current_session)
    ) -> SessionTokenData:
        if permission not in session.permissions:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Permission '{permission}' required"
            )
        return session
    
    return permission_checker


def require_any_permission(permissions: List[str]):
    """Dependency factory for checking if user has any of the specified permissions"""
    async def permission_checker(
        session: SessionTokenData = Depends(get_current_session)
    ) -> SessionTokenData:
        if not any(perm in session.permissions for perm in permissions):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"One of these permissions required: {', '.join(permissions)}"
            )
        return session
    
    return permission_checker


async def get_user_permissions(user_id: int, tenant_id: int, db: AsyncSession) -> List[str]:
    """Get all permissions for a user in a specific tenant"""
    result = await db.execute(
        select(RolePermission.permission)
        .join(Role, RolePermission.role_id == Role.id)
        .join(UserTenant, UserTenant.role_id == Role.id)
        .where(
            UserTenant.user_id == user_id,
            UserTenant.tenant_id == tenant_id,
            UserTenant.is_active == True,
            Role.tenant_id == tenant_id
        )
    )
    
    permissions = [row[0] for row in result.fetchall()]
    return permissions
