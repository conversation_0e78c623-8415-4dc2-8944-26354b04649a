import httpx
from typing import List, Dict, Any, Optional
from core.config import settings


class AccountingServiceClient:
    """Client for communicating with the accounting service"""
    
    def __init__(self, session_token: str):
        self.base_url = settings.accounting_service_url
        self.session_token = session_token
        self.headers = {
            "Authorization": f"Bearer {session_token}",
            "Content-Type": "application/json"
        }
    
    async def get_accounts(
        self, 
        account_type: Optional[str] = None,
        is_active: Optional[bool] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """Get accounts from accounting service"""
        params = {"limit": limit}
        if account_type:
            params["account_type"] = account_type
        if is_active is not None:
            params["is_active"] = is_active
        
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{self.base_url}/accounts/",
                headers=self.headers,
                params=params
            )
            response.raise_for_status()
            return response.json()
    
    async def create_account(self, account_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new account"""
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/accounts/",
                headers=self.headers,
                json=account_data
            )
            response.raise_for_status()
            return response.json()
    
    async def get_account(self, account_id: int) -> Dict[str, Any]:
        """Get specific account"""
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{self.base_url}/accounts/{account_id}",
                headers=self.headers
            )
            response.raise_for_status()
            return response.json()
    
    async def update_account(
        self, 
        account_id: int, 
        account_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Update an account"""
        async with httpx.AsyncClient() as client:
            response = await client.put(
                f"{self.base_url}/accounts/{account_id}",
                headers=self.headers,
                json=account_data
            )
            response.raise_for_status()
            return response.json()
    
    async def delete_account(self, account_id: int) -> Dict[str, Any]:
        """Delete an account"""
        async with httpx.AsyncClient() as client:
            response = await client.delete(
                f"{self.base_url}/accounts/{account_id}",
                headers=self.headers
            )
            response.raise_for_status()
            return response.json()
    
    async def health_check(self) -> Dict[str, Any]:
        """Check if accounting service is healthy"""
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{self.base_url}/health")
            response.raise_for_status()
            return response.json()
