import json
from typing import List, Dict, Any
from ai import AIProviderFactory, ChatMessage
from services.accounting_client import AccountingServiceClient


class AgentService:
    """Main agent service for handling AI-orchestrated accounting tasks"""
    
    def __init__(self, session_token: str):
        self.session_token = session_token
        self.ai_provider = AIProviderFactory.create_provider()
        self.accounting_client = AccountingServiceClient(session_token)
    
    async def chat(self, user_message: str, conversation_history: List[Dict[str, str]] = None) -> str:
        """Handle chat interaction with the AI agent"""
        
        # Build conversation context
        messages = [
            ChatMessage(
                role="system",
                content=self._get_system_prompt()
            )
        ]
        
        # Add conversation history if provided
        if conversation_history:
            for msg in conversation_history[-10:]:  # Keep last 10 messages
                messages.append(ChatMessage(
                    role=msg["role"],
                    content=msg["content"]
                ))
        
        # Add current user message
        messages.append(ChatMessage(
            role="user",
            content=user_message
        ))
        
        # Get AI response
        response = await self.ai_provider.chat_completion(messages)
        
        # Check if the AI wants to perform any actions
        if self._should_perform_action(response.content):
            action_result = await self._perform_action(response.content)
            if action_result:
                # Get a follow-up response with the action result
                messages.append(ChatMessage(role="assistant", content=response.content))
                messages.append(ChatMessage(
                    role="system", 
                    content=f"Action result: {action_result}"
                ))
                
                follow_up = await self.ai_provider.chat_completion(messages)
                return follow_up.content
        
        return response.content
    
    def _get_system_prompt(self) -> str:
        """Get the system prompt for the AI agent"""
        return """You are an AI assistant for a multi-tenant accounting system. You help users with accounting tasks such as:

- Managing chart of accounts
- Creating and viewing accounting entries
- Answering questions about financial data
- Providing accounting guidance and best practices

You have access to the following functions that you can call by including them in your response:
- get_accounts(): Get list of accounts
- create_account(account_number, name, account_type): Create a new account
- get_account(account_id): Get specific account details

When you want to call a function, format it like this:
FUNCTION_CALL: function_name(parameters)

Always be helpful, accurate, and follow Swedish accounting standards (BAS). 
Remember that all data is tenant-isolated - you can only access data for the current user's selected company.

Be conversational and explain your actions clearly to the user."""
    
    def _should_perform_action(self, ai_response: str) -> bool:
        """Check if the AI response contains a function call"""
        return "FUNCTION_CALL:" in ai_response
    
    async def _perform_action(self, ai_response: str) -> str:
        """Parse and execute function calls from AI response"""
        try:
            # Extract function call
            lines = ai_response.split('\n')
            function_line = None
            for line in lines:
                if line.strip().startswith("FUNCTION_CALL:"):
                    function_line = line.strip()[14:].strip()  # Remove "FUNCTION_CALL:"
                    break
            
            if not function_line:
                return None
            
            # Parse function call (simple parsing for demo)
            if function_line.startswith("get_accounts()"):
                accounts = await self.accounting_client.get_accounts()
                return f"Found {len(accounts)} accounts: {json.dumps(accounts, indent=2)}"
            
            elif function_line.startswith("get_account("):
                # Extract account_id
                account_id = int(function_line.split('(')[1].split(')')[0])
                account = await self.accounting_client.get_account(account_id)
                return f"Account details: {json.dumps(account, indent=2)}"
            
            elif function_line.startswith("create_account("):
                # Parse parameters (simplified)
                params_str = function_line.split('(')[1].split(')')[0]
                params = [p.strip().strip('"\'') for p in params_str.split(',')]
                
                if len(params) >= 3:
                    account_data = {
                        "account_number": int(params[0]),
                        "name": params[1],
                        "account_type": params[2]
                    }
                    account = await self.accounting_client.create_account(account_data)
                    return f"Account created successfully: {json.dumps(account, indent=2)}"
                else:
                    return "Error: create_account requires account_number, name, and account_type"
            
            else:
                return f"Unknown function: {function_line}"
                
        except Exception as e:
            return f"Error executing function: {str(e)}"
    
    async def get_capabilities(self) -> Dict[str, Any]:
        """Get agent capabilities and available functions"""
        return {
            "ai_provider": type(self.ai_provider).__name__,
            "available_models": self.ai_provider.get_available_models(),
            "functions": [
                {
                    "name": "get_accounts",
                    "description": "Get list of accounts",
                    "parameters": []
                },
                {
                    "name": "create_account", 
                    "description": "Create a new account",
                    "parameters": ["account_number", "name", "account_type"]
                },
                {
                    "name": "get_account",
                    "description": "Get specific account details", 
                    "parameters": ["account_id"]
                }
            ]
        }
