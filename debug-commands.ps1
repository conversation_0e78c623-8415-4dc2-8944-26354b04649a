# Debug commands for troubleshooting

Write-Host "=== Debugging AI Accounting System ===" -ForegroundColor Yellow

# 1. Check container status
Write-Host "`n1. Container Status:" -ForegroundColor Green
docker-compose ps

# 2. Check if ports are listening
Write-Host "`n2. Port Status:" -ForegroundColor Green
netstat -an | findstr ":8000"
netstat -an | findstr ":8001"

# 3. Test basic connectivity
Write-Host "`n3. Testing Basic Connectivity:" -ForegroundColor Green
try {
    $response8000 = Invoke-WebRequest -Uri "http://localhost:8000/" -TimeoutSec 5
    Write-Host "✓ Port 8000 responds: $($response8000.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "✗ Port 8000 not responding: $($_.Exception.Message)" -ForegroundColor Red
}

try {
    $response8001 = Invoke-WebRequest -Uri "http://localhost:8001/" -TimeoutSec 5
    Write-Host "✓ Port 8001 responds: $($response8001.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "✗ Port 8001 not responding: $($_.Exception.Message)" -ForegroundColor Red
}

# 4. Check logs for errors
Write-Host "`n4. Recent Logs (last 20 lines):" -ForegroundColor Green
Write-Host "--- Accounting Service ---" -ForegroundColor Cyan
docker-compose logs --tail=20 accounting-service

Write-Host "`n--- Agent Service ---" -ForegroundColor Cyan
docker-compose logs --tail=20 agent-service

Write-Host "`n--- Database ---" -ForegroundColor Cyan
docker-compose logs --tail=10 postgres

# 5. Test inside containers
Write-Host "`n5. Testing Inside Containers:" -ForegroundColor Green
Write-Host "Testing accounting service internally..."
try {
    docker-compose exec -T accounting-service curl -s http://localhost:8000/
    Write-Host "✓ Accounting service internal test passed" -ForegroundColor Green
} catch {
    Write-Host "✗ Accounting service internal test failed" -ForegroundColor Red
}

Write-Host "Testing agent service internally..."
try {
    docker-compose exec -T agent-service curl -s http://localhost:8001/
    Write-Host "✓ Agent service internal test passed" -ForegroundColor Green
} catch {
    Write-Host "✗ Agent service internal test failed" -ForegroundColor Red
}

Write-Host "`n=== Debug Complete ===" -ForegroundColor Yellow
Write-Host "If services are not responding, try:" -ForegroundColor Cyan
Write-Host "1. .\setup.ps1 down" -ForegroundColor White
Write-Host "2. .\setup.ps1 build" -ForegroundColor White
Write-Host "3. .\setup.ps1 up" -ForegroundColor White
