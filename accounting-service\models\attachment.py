from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Boolean, LargeBinary
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from database import Base


class Attachment(Base):
    """
    Attachment model - File attachments for each tenant
    """
    __tablename__ = "attachments"

    id = Column(Integer, primary_key=True, index=True)
    tenant_id = Column(Integer, ForeignKey("tenants.id"), nullable=False, index=True)
    filename = Column(String(255), nullable=False)
    original_filename = Column(String(255), nullable=False)
    file_size = Column(Integer, nullable=False)
    mime_type = Column(String(100), nullable=False)
    file_path = Column(String(500))  # Path to file on disk/cloud storage
    file_data = Column(LargeBinary)  # Optional: store small files directly in DB
    
    # What this attachment is related to
    related_entity_type = Column(String(50))  # VOUCHER, INVOICE, etc.
    related_entity_id = Column(Integer)
    
    description = Column(String(500))
    is_active = Column(Boolean, default=True, nullable=False)
    uploaded_by = Column(Integer, ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    tenant = relationship("Tenant", back_populates="attachments")
    uploader = relationship("User")

    def __repr__(self):
        return f"<Attachment(id={self.id}, tenant_id={self.tenant_id}, filename='{self.filename}')>"
