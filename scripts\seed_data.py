#!/usr/bin/env python3
"""
Seed script for populating the database with initial data
"""
import asyncio
import sys
import os
from decimal import Decimal

# Add the accounting-service directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'accounting-service'))

from sqlalchemy.ext.asyncio import AsyncSession
from database import AsyncSessionLocal, engine, Base
from models import (
    Tenant, User, Role, RolePermission, UserTenant, VatCode, Account,
    FinancialYear, CompanySettings, Dimension
)
from core.security import get_password_hash
from datetime import date, datetime


async def create_vat_codes():
    """Create standard Swedish VAT codes"""
    vat_codes = [
        {"code": "0", "name": "Momsf<PERSON>", "rate": Decimal("0.0000"), "description": "Momsfri försäljning"},
        {"code": "6", "name": "Moms 6%", "rate": Decimal("0.0600"), "description": "<PERSON>ucerad moms 6%"},
        {"code": "12", "name": "Moms 12%", "rate": Decimal("0.1200"), "description": "Reducerad moms 12%"},
        {"code": "25", "name": "Moms 25%", "rate": Decimal("0.2500"), "description": "Ordinarie moms 25%"},
        {"code": "IG", "name": "Ingående moms", "rate": Decimal("0.2500"), "description": "Ingående moms"},
        {"code": "UG", "name": "Utgående moms", "rate": Decimal("0.2500"), "description": "Utgående moms"},
    ]
    
    async with AsyncSessionLocal() as session:
        for vat_data in vat_codes:
            vat_code = VatCode(**vat_data)
            session.add(vat_code)
        
        await session.commit()
        print("✓ VAT codes created")


async def create_demo_tenant():
    """Create a demo tenant with complete setup"""
    async with AsyncSessionLocal() as session:
        # Create tenant
        tenant = Tenant(
            company_name="Demo Företag AB",
            is_active=True
        )
        session.add(tenant)
        await session.flush()  # Get the ID
        
        # Create roles with permissions
        admin_role = Role(
            tenant_id=tenant.id,
            name="Administratör",
            description="Full access to all functions"
        )
        session.add(admin_role)
        await session.flush()
        
        # Admin permissions
        admin_permissions = [
            "accounts:create", "accounts:read", "accounts:update", "accounts:delete",
            "vouchers:create", "vouchers:read", "vouchers:update", "vouchers:delete",
            "suppliers:create", "suppliers:read", "suppliers:update", "suppliers:delete",
            "customers:create", "customers:read", "customers:update", "customers:delete",
            "reports:read", "settings:read", "settings:update",
            "users:read", "users:update"
        ]
        
        for permission in admin_permissions:
            role_perm = RolePermission(role_id=admin_role.id, permission=permission)
            session.add(role_perm)
        
        # Create bookkeeper role
        bookkeeper_role = Role(
            tenant_id=tenant.id,
            name="Bokförare",
            description="Standard bookkeeping access"
        )
        session.add(bookkeeper_role)
        await session.flush()
        
        # Bookkeeper permissions
        bookkeeper_permissions = [
            "accounts:read", "vouchers:create", "vouchers:read", "vouchers:update",
            "suppliers:read", "customers:read", "reports:read"
        ]
        
        for permission in bookkeeper_permissions:
            role_perm = RolePermission(role_id=bookkeeper_role.id, permission=permission)
            session.add(role_perm)
        
        # Create demo users
        admin_user = User(
            email="<EMAIL>",
            password_hash=get_password_hash("admin123"),
            first_name="Admin",
            last_name="Användare",
            is_active=True
        )
        session.add(admin_user)
        
        bookkeeper_user = User(
            email="<EMAIL>", 
            password_hash=get_password_hash("bokforare123"),
            first_name="Bok",
            last_name="Förare",
            is_active=True
        )
        session.add(bookkeeper_user)
        await session.flush()
        
        # Link users to tenant
        admin_tenant_link = UserTenant(
            user_id=admin_user.id,
            tenant_id=tenant.id,
            role_id=admin_role.id,
            is_active=True
        )
        session.add(admin_tenant_link)
        
        bookkeeper_tenant_link = UserTenant(
            user_id=bookkeeper_user.id,
            tenant_id=tenant.id,
            role_id=bookkeeper_role.id,
            is_active=True
        )
        session.add(bookkeeper_tenant_link)
        
        # Create financial year
        financial_year = FinancialYear(
            tenant_id=tenant.id,
            year=2024,
            start_date=date(2024, 1, 1),
            end_date=date(2024, 12, 31),
            is_current=True,
            is_closed=False
        )
        session.add(financial_year)
        
        # Create company settings
        company_settings = CompanySettings(
            tenant_id=tenant.id,
            organization_number="556123-4567",
            vat_number="SE556123456701",
            address="Demovägen 123",
            postal_code="12345",
            city="Stockholm",
            country="Sweden",
            phone="08-123 45 67",
            email="<EMAIL>",
            default_currency="SEK",
            fiscal_year_start_month=1,
            chart_of_accounts_template="BAS2024",
            ai_automation_enabled=True
        )
        session.add(company_settings)
        
        await session.commit()
        print(f"✓ Demo tenant created: {tenant.company_name} (ID: {tenant.id})")
        print(f"  - Admin user: <EMAIL> / admin123")
        print(f"  - Bookkeeper: <EMAIL> / bokforare123")
        
        return tenant.id


async def create_chart_of_accounts(tenant_id: int):
    """Create a basic Swedish chart of accounts (BAS 2024)"""
    accounts = [
        # ASSETS (1000-1999)
        {"account_number": 1010, "name": "Utvecklingsutgifter", "account_type": "ASSET"},
        {"account_number": 1020, "name": "Koncessioner, patent, licenser", "account_type": "ASSET"},
        {"account_number": 1030, "name": "Hyresrätter och liknande rättigheter", "account_type": "ASSET"},
        {"account_number": 1040, "name": "Varumärken", "account_type": "ASSET"},
        {"account_number": 1050, "name": "Goodwill", "account_type": "ASSET"},
        
        # Fixed assets
        {"account_number": 1200, "name": "Maskiner och andra tekniska anläggningar", "account_type": "ASSET"},
        {"account_number": 1220, "name": "Inventarier, verktyg och installationer", "account_type": "ASSET"},
        
        # Current assets
        {"account_number": 1510, "name": "Kundfordringar", "account_type": "ASSET"},
        {"account_number": 1630, "name": "Skattekonto", "account_type": "ASSET"},
        {"account_number": 1650, "name": "Momsfordran", "account_type": "ASSET"},
        {"account_number": 1910, "name": "Kassa", "account_type": "ASSET"},
        {"account_number": 1930, "name": "Företagskonto / Affärskonto", "account_type": "ASSET"},
        
        # LIABILITIES (2000-2999)
        {"account_number": 2010, "name": "Aktiekapital", "account_type": "EQUITY"},
        {"account_number": 2018, "name": "Överkursfond", "account_type": "EQUITY"},
        {"account_number": 2070, "name": "Uppskrivningsfond", "account_type": "EQUITY"},
        {"account_number": 2080, "name": "Reservfond", "account_type": "EQUITY"},
        {"account_number": 2090, "name": "Balanserad vinst eller förlust", "account_type": "EQUITY"},
        {"account_number": 2099, "name": "Årets resultat", "account_type": "EQUITY"},
        
        # Long-term liabilities
        {"account_number": 2330, "name": "Banklån", "account_type": "LIABILITY"},
        
        # Current liabilities
        {"account_number": 2440, "name": "Leverantörsskulder", "account_type": "LIABILITY"},
        {"account_number": 2510, "name": "Skatteskulder", "account_type": "LIABILITY"},
        {"account_number": 2610, "name": "Utgående moms", "account_type": "LIABILITY"},
        {"account_number": 2640, "name": "Ingående moms", "account_type": "ASSET"},
        
        # REVENUE (3000-3999)
        {"account_number": 3010, "name": "Försäljning varor, 25% moms", "account_type": "REVENUE"},
        {"account_number": 3011, "name": "Försäljning varor, 12% moms", "account_type": "REVENUE"},
        {"account_number": 3012, "name": "Försäljning varor, 6% moms", "account_type": "REVENUE"},
        {"account_number": 3013, "name": "Försäljning varor, momsfritt", "account_type": "REVENUE"},
        {"account_number": 3040, "name": "Försäljning tjänster, 25% moms", "account_type": "REVENUE"},
        {"account_number": 3041, "name": "Försäljning tjänster, 12% moms", "account_type": "REVENUE"},
        {"account_number": 3042, "name": "Försäljning tjänster, 6% moms", "account_type": "REVENUE"},
        {"account_number": 3043, "name": "Försäljning tjänster, momsfritt", "account_type": "REVENUE"},
        
        # EXPENSES (4000-8999)
        {"account_number": 4010, "name": "Inköp av varor", "account_type": "EXPENSE"},
        {"account_number": 5010, "name": "Lokalhyra", "account_type": "EXPENSE"},
        {"account_number": 5020, "name": "Elektricitet", "account_type": "EXPENSE"},
        {"account_number": 5030, "name": "Uppvärmning", "account_type": "EXPENSE"},
        {"account_number": 5040, "name": "Vatten och avlopp", "account_type": "EXPENSE"},
        {"account_number": 5060, "name": "Städning", "account_type": "EXPENSE"},
        {"account_number": 5410, "name": "Förbrukningsinventarier", "account_type": "EXPENSE"},
        {"account_number": 5420, "name": "Programvaror", "account_type": "EXPENSE"},
        {"account_number": 5460, "name": "Förbrukningsmaterial", "account_type": "EXPENSE"},
        {"account_number": 6110, "name": "Kontorsmaterial", "account_type": "EXPENSE"},
        {"account_number": 6210, "name": "Telefon", "account_type": "EXPENSE"},
        {"account_number": 6220, "name": "Internet", "account_type": "EXPENSE"},
        {"account_number": 6250, "name": "Datakommunikation", "account_type": "EXPENSE"},
        {"account_number": 6310, "name": "Marknadsföring", "account_type": "EXPENSE"},
        {"account_number": 6420, "name": "Representation", "account_type": "EXPENSE"},
        {"account_number": 6540, "name": "IT-tjänster", "account_type": "EXPENSE"},
        {"account_number": 6570, "name": "Redovisningstjänster", "account_type": "EXPENSE"},
        {"account_number": 6580, "name": "Juridiska tjänster", "account_type": "EXPENSE"},
        {"account_number": 6590, "name": "Övriga externa tjänster", "account_type": "EXPENSE"},
        {"account_number": 7010, "name": "Löner", "account_type": "EXPENSE"},
        {"account_number": 7510, "name": "Arbetsgivaravgifter", "account_type": "EXPENSE"},
        {"account_number": 7610, "name": "Pensionskostnader", "account_type": "EXPENSE"},
        {"account_number": 8300, "name": "Ränteintäkter", "account_type": "REVENUE"},
        {"account_number": 8400, "name": "Räntekostnader", "account_type": "EXPENSE"},
        {"account_number": 8410, "name": "Dröjsmålsränta", "account_type": "EXPENSE"},
        {"account_number": 8420, "name": "Fakturaavgifter", "account_type": "EXPENSE"}
    ]
    
    async with AsyncSessionLocal() as session:
        for account_data in accounts:
            account = Account(
                tenant_id=tenant_id,
                **account_data,
                is_active=True,
                balance=Decimal("0.00")
            )
            session.add(account)
        
        await session.commit()
        print(f"✓ Chart of accounts created ({len(accounts)} accounts)")


async def create_dimensions(tenant_id: int):
    """Create sample dimensions (cost centers, projects)"""
    dimensions = [
        # Cost centers
        {"dimension_type": "COST_CENTER", "code": "ADM", "name": "Administration"},
        {"dimension_type": "COST_CENTER", "code": "SAL", "name": "Försäljning"},
        {"dimension_type": "COST_CENTER", "code": "PRD", "name": "Produktion"},
        {"dimension_type": "COST_CENTER", "code": "IT", "name": "IT-avdelning"},
        
        # Projects
        {"dimension_type": "PROJECT", "code": "PROJ001", "name": "Utvecklingsprojekt Alpha"},
        {"dimension_type": "PROJECT", "code": "PROJ002", "name": "Kundprojekt Beta"},
        {"dimension_type": "PROJECT", "code": "PROJ003", "name": "Marknadsföringskampanj"},
        
        # Departments
        {"dimension_type": "DEPARTMENT", "code": "HR", "name": "Personal"},
        {"dimension_type": "DEPARTMENT", "code": "FIN", "name": "Ekonomi"},
        {"dimension_type": "DEPARTMENT", "code": "OPS", "name": "Drift"}
    ]
    
    async with AsyncSessionLocal() as session:
        for dim_data in dimensions:
            dimension = Dimension(
                tenant_id=tenant_id,
                **dim_data,
                is_active=True
            )
            session.add(dimension)
        
        await session.commit()
        print(f"✓ Dimensions created ({len(dimensions)} dimensions)")


async def main():
    """Main seeding function"""
    print("🌱 Starting database seeding...")
    
    # Create all tables
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    print("✓ Database tables created")
    
    # Seed data
    await create_vat_codes()
    tenant_id = await create_demo_tenant()
    await create_chart_of_accounts(tenant_id)
    await create_dimensions(tenant_id)
    
    print("\n🎉 Database seeding completed!")
    print("\nYou can now:")
    print("1. Start the application: docker-compose up")
    print("2. Login as admin: <EMAIL> / admin123")
    print("3. Login as bookkeeper: <EMAIL> / bokforare123")
    print("4. Test the API at: http://localhost:8000/docs")


if __name__ == "__main__":
    asyncio.run(main())
