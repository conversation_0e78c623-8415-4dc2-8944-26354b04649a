from sqlalchemy import Column, <PERSON>te<PERSON>, <PERSON>, <PERSON><PERSON>an, DateTime, ForeignKey, Numeric
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from database import Base


class Account(Base):
    """
    Account model - Chart of accounts for each tenant
    """
    __tablename__ = "accounts"

    id = Column(Integer, primary_key=True, index=True)
    tenant_id = Column(Integer, ForeignKey("tenants.id"), nullable=False, index=True)
    account_number = Column(Integer, nullable=False, index=True)
    name = Column(String(255), nullable=False)
    account_type = Column(String(50), nullable=False)  # ASSET, LIABILITY, EQUITY, REVENUE, EXPENSE
    parent_account_id = Column(Integer, ForeignKey("accounts.id"), nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)
    balance = Column(Numeric(15, 2), default=0, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    tenant = relationship("Tenant", back_populates="accounts")
    parent_account = relationship("Account", remote_side=[id])
    voucher_rows = relationship("VoucherRow", back_populates="account")

    def __repr__(self):
        return f"<Account(id={self.id}, tenant_id={self.tenant_id}, account_number={self.account_number}, name='{self.name}')>"
