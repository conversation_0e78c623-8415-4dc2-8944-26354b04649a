from sqlalchemy import Column, Integer, String, Numeric, Boolean, DateTime
from sqlalchemy.sql import func
from database import Base


class VatCode(Base):
    """
    VatCode model - Global VAT codes (not tenant-specific)
    """
    __tablename__ = "vat_codes"

    id = Column(Integer, primary_key=True, index=True)
    code = Column(String(10), unique=True, nullable=False, index=True)
    name = Column(String(100), nullable=False)
    rate = Column(Numeric(5, 4), nullable=False)  # e.g., 0.2500 for 25%
    description = Column(String(500))
    is_active = Column(Boolean, default=True, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    def __repr__(self):
        return f"<VatCode(id={self.id}, code='{self.code}', rate={self.rate})>"
