# AI Accounting System - Windows Setup Script

param(
    [string]$Command = "help"
)

function Show-Help {
    Write-Host "AI Accounting System - Windows Setup" -ForegroundColor Green
    Write-Host ""
    Write-Host "Usage: .\setup.ps1 [command]" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Commands:" -ForegroundColor Yellow
    Write-Host "  help        Show this help message"
    Write-Host "  build       Build all Docker images"
    Write-Host "  up          Start all services"
    Write-Host "  down        Stop all services"
    Write-Host "  logs        Show logs from all services"
    Write-Host "  seed        Seed the database with initial data"
    Write-Host "  reset       Reset everything (clean + build + up + seed)"
    Write-Host "  clean       Clean up Docker resources"
    Write-Host "  test        Run tests"
    Write-Host ""
    Write-Host "Examples:" -ForegroundColor Cyan
    Write-Host "  .\setup.ps1 reset     # Full setup"
    Write-Host "  .\setup.ps1 up        # Start services"
    Write-Host "  .\setup.ps1 logs      # View logs"
}

function Build-Services {
    Write-Host "Building Docker images..." -ForegroundColor Green
    docker-compose build
}

function Start-Services {
    Write-Host "Starting services..." -ForegroundColor Green
    docker-compose up -d
    Write-Host "Services started! Check status with: docker-compose ps" -ForegroundColor Green
}

function Stop-Services {
    Write-Host "Stopping services..." -ForegroundColor Green
    docker-compose down
}

function Show-Logs {
    Write-Host "Showing logs (Ctrl+C to exit)..." -ForegroundColor Green
    docker-compose logs -f
}

function Seed-Database {
    Write-Host "Seeding database..." -ForegroundColor Green
    
    # Run seed script
    Push-Location accounting-service
    try {
        python ../scripts/seed_data.py
        Write-Host "Database seeded successfully!" -ForegroundColor Green
    }
    catch {
        Write-Host "Error seeding database: $_" -ForegroundColor Red
    }
    finally {
        Pop-Location
    }
}

function Clean-Docker {
    Write-Host "Cleaning Docker resources..." -ForegroundColor Green
    docker-compose down -v
    docker system prune -f
    Write-Host "Docker resources cleaned!" -ForegroundColor Green
}

function Reset-Everything {
    Write-Host "Resetting everything..." -ForegroundColor Green
    Clean-Docker
    Build-Services
    Start-Services
    Write-Host "Waiting for services to be ready..." -ForegroundColor Yellow
    Start-Sleep -Seconds 15
    Seed-Database
    
    Write-Host ""
    Write-Host "Setup complete!" -ForegroundColor Green
    Write-Host ""
    Write-Host "Services are running at:" -ForegroundColor Cyan
    Write-Host "  Accounting API: http://localhost:8000/docs"
    Write-Host "  Agent API:      http://localhost:8001/docs"
    Write-Host ""
    Write-Host "Demo users:" -ForegroundColor Cyan
    Write-Host "  Admin:      <EMAIL> / admin123"
    Write-Host "  Bookkeeper: <EMAIL> / bokforare123"
}

function Run-Tests {
    Write-Host "Running tests..." -ForegroundColor Green
    Push-Location accounting-service
    try {
        python -m pytest tests/ -v
    }
    catch {
        Write-Host "Error running tests: $_" -ForegroundColor Red
    }
    finally {
        Pop-Location
    }
}

# Main script logic
switch ($Command.ToLower()) {
    "help" { Show-Help }
    "build" { Build-Services }
    "up" { Start-Services }
    "down" { Stop-Services }
    "logs" { Show-Logs }
    "seed" { Seed-Database }
    "clean" { Clean-Docker }
    "reset" { Reset-Everything }
    "test" { Run-Tests }
    default { 
        Write-Host "Unknown command: $Command" -ForegroundColor Red
        Show-Help 
    }
}
