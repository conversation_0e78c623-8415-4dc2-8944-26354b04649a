version: '3.8'

services:
  postgres:
    image: postgres:15
    container_name: ai_accounting_postgres_test
    environment:
      POSTGRES_DB: ai_accounting
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_test_data:/var/lib/postgresql/data

  accounting-service-test:
    build: 
      context: ./accounting-service
      dockerfile: Dockerfile
    container_name: ai_accounting_service_test
    environment:
      DATABASE_URL: ********************************************/ai_accounting
    ports:
      - "8000:8000"
    depends_on:
      - postgres
    volumes:
      - ./accounting-service:/app
    command: uvicorn test_main:app --host 0.0.0.0 --port 8000 --reload

volumes:
  postgres_test_data:
