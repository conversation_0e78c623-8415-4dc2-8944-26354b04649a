from pydantic import BaseModel, EmailStr
from typing import List, Optional


class UserLogin(BaseModel):
    """User login schema"""
    email: EmailStr
    password: str


class UserRegister(BaseModel):
    """User registration schema"""
    email: EmailStr
    password: str
    first_name: Optional[str] = None
    last_name: Optional[str] = None


class Token(BaseModel):
    """Access token response"""
    access_token: str
    token_type: str = "bearer"


class SessionToken(BaseModel):
    """Session token response with tenant context"""
    session_token: str
    token_type: str = "bearer"
    tenant_id: int
    tenant_name: str
    permissions: List[str]


class TenantSelection(BaseModel):
    """Tenant selection for session creation"""
    tenant_id: int
