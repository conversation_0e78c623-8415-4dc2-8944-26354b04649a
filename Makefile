.PHONY: help build up down logs seed test clean

help: ## Show this help message
	@echo 'Usage: make [target]'
	@echo ''
	@echo 'Targets:'
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  %-15s %s\n", $$1, $$2}' $(MAKEFILE_LIST)

build: ## Build all Docker images
	docker-compose build

up: ## Start all services
	docker-compose up -d

down: ## Stop all services
	docker-compose down

logs: ## Show logs from all services
	docker-compose logs -f

logs-accounting: ## Show logs from accounting service
	docker-compose logs -f accounting-service

logs-agent: ## Show logs from agent service
	docker-compose logs -f agent-service

logs-db: ## Show logs from database
	docker-compose logs -f postgres

seed: ## Seed the database with initial data
	cd accounting-service && python ../scripts/seed_data.py

test: ## Run tests
	cd accounting-service && python -m pytest tests/ -v

test-coverage: ## Run tests with coverage
	cd accounting-service && python -m pytest tests/ --cov=. --cov-report=html

clean: ## Clean up Docker resources
	docker-compose down -v
	docker system prune -f

reset: ## Reset everything (clean + build + up + seed)
	make clean
	make build
	make up
	sleep 10
	make seed

dev-setup: ## Setup development environment
	@echo "Setting up development environment..."
	cp .env.example .env
	@echo "Please edit .env file with your configuration"
	@echo "Then run: make reset"

# Database operations
db-shell: ## Connect to database shell
	docker-compose exec postgres psql -U postgres -d ai_accounting

db-backup: ## Backup database
	docker-compose exec postgres pg_dump -U postgres ai_accounting > backup_$(shell date +%Y%m%d_%H%M%S).sql

# Development helpers
accounting-shell: ## Shell into accounting service container
	docker-compose exec accounting-service bash

agent-shell: ## Shell into agent service container
	docker-compose exec agent-service bash

install-deps: ## Install Python dependencies locally
	cd accounting-service && pip install -r requirements.txt
	cd agent-service && pip install -r requirements.txt
